
@page "/job-history"
@using ScheduleDot.Core
@inject IJSRuntime JSRuntime

<PageTitle>Job History</PageTitle>

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h3>Job History</h3>
                <a href="/job-definition" class="btn btn-primary">
                    <i class="fas fa-plus"></i> Create New Job
                </a>
            </div>

            @if (!string.IsNullOrEmpty(errorMessage))
            {
                <div class="alert alert-danger" role="alert">
                    @errorMessage
                </div>
            }

            @if (isLoading)
            {
                <div class="text-center">
                    <div class="spinner-border" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                    <p class="mt-2">Loading jobs...</p>
                </div>
            }
            else if (jobs == null || !jobs.Any())
            {
                <div class="text-center">
                    <div class="alert alert-info">
                        <h5>No jobs found</h5>
                        <p>You haven't created any jobs yet. <a href="/job-definition">Create your first job</a> to get started.</p>
                    </div>
                </div>
            }
            else
            {
                <!-- Filter and Search Controls -->
                <div class="row mb-3">
                    <div class="col-md-4">
                        <div class="input-group">
                            <span class="input-group-text"><i class="fas fa-search"></i></span>
                            <input type="text" class="form-control" placeholder="Search jobs..." @bind="searchTerm" @oninput="OnSearchChanged" />
                        </div>
                    </div>
                    <div class="col-md-3">
                        <select class="form-select" @bind="statusFilter" @bind:after="OnFilterChanged">
                            <option value="">All Statuses</option>
                            @foreach (var status in Enum.GetValues<JobStatus>())
                            {
                                <option value="@status">@status</option>
                            }
                        </select>
                    </div>
                    <div class="col-md-3">
                        <select class="form-select" @bind="typeFilter" @bind:after="OnFilterChanged">
                            <option value="">All Types</option>
                            @foreach (var type in Enum.GetValues<JobType>())
                            {
                                <option value="@type">@GetJobTypeDisplayName(type)</option>
                            }
                        </select>
                    </div>
                    <div class="col-md-2">
                        <button class="btn btn-outline-secondary w-100" @onclick="RefreshJobs">
                            <i class="fas fa-refresh"></i> Refresh
                        </button>
                    </div>
                </div>

                <!-- Jobs Table -->
                <div class="table-responsive">
                    <table class="table table-striped table-hover">
                        <thead class="table-dark">
                            <tr>
                                <th>Name</th>
                                <th>Type</th>
                                <th>Status</th>
                                <th>Frequency</th>
                                <th>Priority</th>
                                <th>Start Time</th>
                                <th>Last Run</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach (var job in filteredJobs)
                            {
                                <tr>
                                    <td>
                                        <div class="fw-bold">@job.Name</div>
                                        @if (!string.IsNullOrEmpty(job.Description))
                                        {
                                            <small class="text-muted">@job.Description</small>
                                        }
                                    </td>
                                    <td>
                                        <span class="badge bg-info">@GetJobTypeDisplayName(job.JobType)</span>
                                    </td>
                                    <td>
                                        <span class="badge @GetStatusBadgeClass(job.Status)">@job.Status</span>
                                    </td>
                                    <td>@job.JobFrequency</td>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            @for (int i = 1; i <= 10; i++)
                                            {
                                                <i class="fas fa-star @(i <= job.Priority ? "text-warning" : "text-muted")" style="font-size: 0.8em;"></i>
                                            }
                                            <span class="ms-1 small">(@job.Priority)</span>
                                        </div>
                                    </td>
                                    <td>@job.StartTime.ToString("yyyy-MM-dd HH:mm")</td>
                                    <td>
                                        @if (jobHistories.ContainsKey(job.Id) && jobHistories[job.Id].Any())
                                        {
                                            var lastRun = jobHistories[job.Id].First();
                                            <div>
                                                <small class="@(lastRun.Success ? "text-success" : "text-danger")">
                                                    @lastRun.ExecutionTime.ToString("yyyy-MM-dd HH:mm")
                                                    <i class="fas @(lastRun.Success ? "fa-check-circle" : "fa-times-circle")"></i>
                                                </small>
                                            </div>
                                        }
                                        else
                                        {
                                            <small class="text-muted">Never run</small>
                                        }
                                    </td>
                                    <td>
                                        <div class="btn-group btn-group-sm" role="group">
                                            <a href="/job-details/@job.Id" class="btn btn-outline-primary" title="View Details">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <a href="/job-definition/@job.Id" class="btn btn-outline-secondary" title="Edit">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <button class="btn btn-outline-success" @onclick="() => RunJob(job.Id)" title="Run Now" disabled="@(job.Status != JobStatus.Published)">
                                                <i class="fas fa-play"></i>
                                            </button>
                                            <button class="btn btn-outline-danger" @onclick="() => DeleteJob(job.Id)" title="Delete">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                            }
                        </tbody>
                    </table>
                </div>

                <!-- Pagination -->
                @if (totalPages > 1)
                {
                    <nav aria-label="Job pagination">
                        <ul class="pagination justify-content-center">
                            <li class="page-item @(currentPage == 1 ? "disabled" : "")">
                                <button class="page-link" @onclick="() => ChangePage(currentPage - 1)" disabled="@(currentPage == 1)">Previous</button>
                            </li>

                            @for (int i = Math.Max(1, currentPage - 2); i <= Math.Min(totalPages, currentPage + 2); i++)
                            {
                                <li class="page-item @(i == currentPage ? "active" : "")">
                                    <button class="page-link" @onclick="() => ChangePage(i)">@i</button>
                                </li>
                            }

                            <li class="page-item @(currentPage == totalPages ? "disabled" : "")">
                                <button class="page-link" @onclick="() => ChangePage(currentPage + 1)" disabled="@(currentPage == totalPages)">Next</button>
                            </li>
                        </ul>
                    </nav>
                }
            }
        </div>
    </div>
</div>

@code {
    private List<Job> jobs = new();
    private List<Job> filteredJobs = new();
    private Dictionary<int, List<JobHistory>> jobHistories = new();

    private string errorMessage = string.Empty;
    private bool isLoading = true;

    // Filter and search
    private string searchTerm = string.Empty;
    private JobStatus? statusFilter = null;
    private JobType? typeFilter = null;

    // Pagination
    private int currentPage = 1;
    private int pageSize = 10;
    private int totalPages = 1;

    protected override async Task OnInitializedAsync()
    {
        await LoadJobs();
    }

    private async Task LoadJobs()
    {
        isLoading = true;
        errorMessage = string.Empty;

        try
        {
            // TODO: Load jobs from API
            // For now, we'll create some sample data
            jobs = GenerateSampleJobs();

            // TODO: Load job histories from API
            jobHistories = GenerateSampleJobHistories();

            ApplyFilters();
        }
        catch (Exception ex)
        {
            errorMessage = $"Error loading jobs: {ex.Message}";
        }
        finally
        {
            isLoading = false;
        }
    }

    private List<Job> GenerateSampleJobs()
    {
        return new List<Job>
        {
            new Job
            {
                Id = 1,
                Name = "Daily Data Backup",
                Description = "Backup database to cloud storage",
                JobType = JobType.ExeFile,
                JobFrequency = JobFrequency.Daily,
                Status = JobStatus.Published,
                Priority = 8,
                StartTime = DateTime.Now.AddDays(-30),
                EndTime = DateTime.Now.AddDays(365),
                ExeFilePath = "C:\\Scripts\\backup.exe"
            },
            new Job
            {
                Id = 2,
                Name = "API Health Check",
                Description = "Monitor API endpoint availability",
                JobType = JobType.ApiEndpoint,
                JobFrequency = JobFrequency.Hourly,
                Status = JobStatus.Published,
                Priority = 9,
                StartTime = DateTime.Now.AddDays(-7),
                EndTime = DateTime.Now.AddDays(90),
                ApiEndpointAddress = "https://api.example.com/health"
            },
            new Job
            {
                Id = 3,
                Name = "Generate Reports",
                Description = "Generate monthly sales reports",
                JobType = JobType.DynamicCode,
                JobFrequency = JobFrequency.Monthly,
                Status = JobStatus.Draft,
                Priority = 5,
                StartTime = DateTime.Now,
                EndTime = DateTime.Now.AddDays(365),
                DynamicCode = "Console.WriteLine(\"Generating reports...\");"
            }
        };
    }

    private Dictionary<int, List<JobHistory>> GenerateSampleJobHistories()
    {
        return new Dictionary<int, List<JobHistory>>
        {
            [1] = new List<JobHistory>
            {
                new JobHistory { Id = 1, JobId = 1, ExecutionTime = DateTime.Now.AddHours(-2), Success = true, Log = "Backup completed successfully" },
                new JobHistory { Id = 2, JobId = 1, ExecutionTime = DateTime.Now.AddDays(-1), Success = true, Log = "Backup completed successfully" }
            },
            [2] = new List<JobHistory>
            {
                new JobHistory { Id = 3, JobId = 2, ExecutionTime = DateTime.Now.AddMinutes(-30), Success = true, Log = "API is healthy" },
                new JobHistory { Id = 4, JobId = 2, ExecutionTime = DateTime.Now.AddHours(-1), Success = false, Log = "API timeout error" }
            }
        };
    }

    private void ApplyFilters()
    {
        var filtered = jobs.AsEnumerable();

        // Apply search filter
        if (!string.IsNullOrWhiteSpace(searchTerm))
        {
            filtered = filtered.Where(j =>
                j.Name.Contains(searchTerm, StringComparison.OrdinalIgnoreCase) ||
                (j.Description?.Contains(searchTerm, StringComparison.OrdinalIgnoreCase) ?? false));
        }

        // Apply status filter
        if (statusFilter.HasValue)
        {
            filtered = filtered.Where(j => j.Status == statusFilter.Value);
        }

        // Apply type filter
        if (typeFilter.HasValue)
        {
            filtered = filtered.Where(j => j.JobType == typeFilter.Value);
        }

        var allFiltered = filtered.ToList();
        totalPages = (int)Math.Ceiling((double)allFiltered.Count / pageSize);

        // Apply pagination
        filteredJobs = allFiltered
            .Skip((currentPage - 1) * pageSize)
            .Take(pageSize)
            .ToList();
    }

    private async Task OnSearchChanged(ChangeEventArgs e)
    {
        searchTerm = e.Value?.ToString() ?? string.Empty;
        currentPage = 1;
        ApplyFilters();
        await Task.CompletedTask;
    }

    private async Task OnFilterChanged()
    {
        currentPage = 1;
        ApplyFilters();
        await Task.CompletedTask;
    }

    private void ChangePage(int page)
    {
        if (page >= 1 && page <= totalPages)
        {
            currentPage = page;
            ApplyFilters();
        }
    }

    private async Task RefreshJobs()
    {
        await LoadJobs();
    }

    private async Task RunJob(int jobId)
    {
        try
        {
            // TODO: Call API to run job
            errorMessage = string.Empty;
            // For now, just show a message
            await JSRuntime.InvokeVoidAsync("alert", $"Job {jobId} execution started!");
        }
        catch (Exception ex)
        {
            errorMessage = $"Error running job: {ex.Message}";
        }
    }

    private async Task DeleteJob(int jobId)
    {
        try
        {
            var confirmed = await JSRuntime.InvokeAsync<bool>("confirm", "Are you sure you want to delete this job?");
            if (confirmed)
            {
                // TODO: Call API to delete job
                jobs.RemoveAll(j => j.Id == jobId);
                ApplyFilters();
                errorMessage = string.Empty;
            }
        }
        catch (Exception ex)
        {
            errorMessage = $"Error deleting job: {ex.Message}";
        }
    }

    private string GetJobTypeDisplayName(JobType jobType)
    {
        return jobType switch
        {
            JobType.ApiEndpoint => "API Endpoint",
            JobType.ExeFile => "Executable File",
            JobType.DynamicCode => "Dynamic Code",
            _ => jobType.ToString()
        };
    }

    private string GetStatusBadgeClass(JobStatus status)
    {
        return status switch
        {
            JobStatus.Draft => "bg-secondary",
            JobStatus.Pending => "bg-warning",
            JobStatus.Published => "bg-success",
            _ => "bg-secondary"
        };
    }
}
