
using Microsoft.EntityFrameworkCore;
using ScheduleDot.Core;
using ScheduleDot.Api.DataAccess;

namespace ScheduleDot.Api.Services
{
    public class JobService
    {
        private readonly AppDbContext _context;

        public JobService(AppDbContext context)
        {
            _context = context;
        }

        public async Task<IEnumerable<Job>> GetJobsAsync()
        {
            return await _context.Jobs
                .OrderByDescending(j => j.Id)
                .ToListAsync();
        }

        public async Task<Job?> GetJobAsync(int id)
        {
            return await _context.Jobs
                .FirstOrDefaultAsync(j => j.Id == id);
        }

        public async Task<Job> CreateJobAsync(Job job)
        {
            // Set default values for new job
            job.Id = 0; // Let the database generate the ID

            _context.Jobs.Add(job);
            await _context.SaveChangesAsync();

            return job;
        }

        public async Task<Job?> UpdateJobAsync(int id, Job job)
        {
            var existingJob = await _context.Jobs.FindAsync(id);
            if (existingJob == null)
            {
                return null;
            }

            // Update properties
            existingJob.Name = job.Name;
            existingJob.JobType = job.JobType;
            existingJob.JobFrequency = job.JobFrequency;
            existingJob.StartTime = job.StartTime;
            existingJob.EndTime = job.EndTime;
            existingJob.Parameters = job.Parameters;
            existingJob.Description = job.Description;
            existingJob.Status = job.Status;
            existingJob.Priority = job.Priority;
            existingJob.RetryCount = job.RetryCount;
            existingJob.RetryDelay = job.RetryDelay;
            existingJob.EmailNotification = job.EmailNotification;
            existingJob.ApiEndpointAddress = job.ApiEndpointAddress;
            existingJob.ExeFilePath = job.ExeFilePath;
            existingJob.DynamicCode = job.DynamicCode;

            await _context.SaveChangesAsync();
            return existingJob;
        }

        public async Task<bool> DeleteJobAsync(int id)
        {
            var job = await _context.Jobs.FindAsync(id);
            if (job == null)
            {
                return false;
            }

            _context.Jobs.Remove(job);
            await _context.SaveChangesAsync();
            return true;
        }

        public async Task<IEnumerable<JobHistory>> GetJobHistoryAsync(int jobId)
        {
            return await _context.JobHistories
                .Where(jh => jh.JobId == jobId)
                .OrderByDescending(jh => jh.ExecutionTime)
                .ToListAsync();
        }

        public async Task<JobHistory> AddJobHistoryAsync(JobHistory jobHistory)
        {
            _context.JobHistories.Add(jobHistory);
            await _context.SaveChangesAsync();
            return jobHistory;
        }
    }
}
