
@page "/job-history"
@using ScheduleDot.Core
@inject IJSRuntime JSRuntime

<PageTitle>Job HistoryV</PageTitle>

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h3>Job History</h3>
                <a href="/job-definition" class="btn btn-primary">
                    <i class="fas fa-plus"></i> Create New Job
                </a>
            </div>

            @if (!string.IsNullOrEmpty(errorMessage))
            {
                <div class="alert alert-danger" role="alert">
                    @errorMessage
                </div>
            }

            @if (isLoading)
            {
                <div class="text-center">
                    <div class="spinner-border" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                    <p class="mt-2">Loading jobs...</p>
                </div>
            }
            else if (jobs == null || !jobs.Any())
            {
                <div class="text-center">
                    <div class="alert alert-info">
                        <h5>No jobs found</h5>
                        <p>You haven't created any jobs yet. <a href="/job-definition">Create your first job</a> to get started.</p>
                    </div>
                </div>
            }
            else
            {
                <!-- Filter and Search Controls -->
                <div class="row mb-3">
                    <div class="col-md-4">
                        <div class="input-group">
                            <span class="input-group-text"><i class="fas fa-search"></i></span>
                            <input type="text" class="form-control" placeholder="Search jobs..." @bind="searchTerm" @oninput="OnSearchChanged" />
                        </div>
                    </div>
                    <div class="col-md-3">
                        <select class="form-select" @bind="statusFilter" @bind:after="OnFilterChanged">
                            <option value="">All Statuses</option>
                            @foreach (var status in Enum.GetValues<JobStatus>())
                            {
                                <option value="@status">@status</option>
                            }
                        </select>
                    </div>
                    <div class="col-md-3">
                        <select class="form-select" @bind="typeFilter" @bind:after="OnFilterChanged">
                            <option value="">All Types</option>
                            @foreach (var type in Enum.GetValues<JobType>())
                            {
                                <option value="@type">@GetJobTypeDisplayName(type)</option>
                            }
                        </select>
                    </div>
                    <div class="col-md-2">
                        <button class="btn btn-outline-secondary w-100" @onclick="RefreshJobs">
                            <i class="fas fa-refresh"></i> Refresh
                        </button>
                    </div>
                </div>

                <!-- Jobs Table -->
                <div class="table-responsive">
                    <table class="table table-striped table-hover">
                        <thead class="table-dark">
                            <tr>
                                <th>Name</th>
                                <th>Type</th>
                                <th>Status</th>
                                <th>Frequency</th>
                                <th>Priority</th>
                                <th>Start Time</th>
                                <th>Last Run</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach (var job in filteredJobs)
                            {
                                <tr>
                                    <td>
                                        <div class="fw-bold">@job.Name</div>
                                        @if (!string.IsNullOrEmpty(job.Description))
                                        {
                                            <small class="text-muted">@job.Description</small>
                                        }
                                    </td>
                                    <td>
                                        <span class="badge bg-info">@GetJobTypeDisplayName(job.JobType)</span>
                                    </td>
                                    <td>
                                        <span class="badge @GetStatusBadgeClass(job.Status)">@job.Status</span>
                                    </td>
                                    <td>@job.JobFrequency</td>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            @for (int i = 1; i <= 10; i++)
                                            {
                                                <i class="fas fa-star @(i <= job.Priority ? "text-warning" : "text-muted")" style="font-size: 0.8em;"></i>
                                            }
                                            <span class="ms-1 small">(@job.Priority)</span>
                                        </div>
                                    </td>
                                    <td>@job.StartTime.ToString("yyyy-MM-dd HH:mm")</td>
                                    <td>
                                        @if (jobHistories.ContainsKey(job.Id) && jobHistories[job.Id].Any())
                                        {
                                            var lastRun = jobHistories[job.Id].First();
                                            <div>
                                                <small class="@(lastRun.Success ? "text-success" : "text-danger")">
                                                    @lastRun.ExecutionTime.ToString("yyyy-MM-dd HH:mm")
                                                    <i class="fas @(lastRun.Success ? "fa-check-circle" : "fa-times-circle")"></i>
                                                </small>
                                            </div>
                                        }
                                        else
                                        {
                                            <small class="text-muted">Never run</small>
                                        }
                                    </td>
                                    <td>
                                        <div class="btn-group btn-group-sm" role="group">
                                            <a href="/job-details/@job.Id" class="btn btn-outline-primary" title="View Details">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <a href="/job-definition/@job.Id" class="btn btn-outline-secondary" title="Edit">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <button class="btn btn-outline-success" @onclick="() => RunJob(job.Id)" title="Run Now" disabled="@(job.Status != JobStatus.Published)">
                                                <i class="fas fa-play"></i>
                                            </button>
                                            <button class="btn btn-outline-danger" @onclick="() => DeleteJob(job.Id)" title="Delete">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                            }
                        </tbody>
                    </table>
                </div>

                <!-- Pagination -->
                @if (totalPages > 1)
                {
                    <nav aria-label="Job pagination">
                        <ul class="pagination justify-content-center">
                            <li class="page-item @(currentPage == 1 ? "disabled" : "")">
                                <button class="page-link" @onclick="() => ChangePage(currentPage - 1)" disabled="@(currentPage == 1)">Previous</button>
                            </li>

                            @for (int i = Math.Max(1, currentPage - 2); i <= Math.Min(totalPages, currentPage + 2); i++)
                            {
                                <li class="page-item @(i == currentPage ? "active" : "")">
                                    <button class="page-link" @onclick="() => ChangePage(i)">@i</button>
                                </li>
                            }

                            <li class="page-item @(currentPage == totalPages ? "disabled" : "")">
                                <button class="page-link" @onclick="() => ChangePage(currentPage + 1)" disabled="@(currentPage == totalPages)">Next</button>
                            </li>
                        </ul>
                    </nav>
                }
            }
        </div>
    </div>
</div>


