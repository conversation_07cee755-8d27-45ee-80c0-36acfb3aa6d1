
using System.Diagnostics;
using System.Net.Http;
using System.Text;
using Microsoft.CodeAnalysis.CSharp.Scripting;
using Microsoft.CodeAnalysis.Scripting;
using ScheduleDot.Core;

namespace ScheduleDot.Api.Services
{
    public class SchedulingService
    {
        private readonly JobService _jobService;
        private readonly IHttpClientFactory _httpClientFactory;
        private readonly ILogger<SchedulingService> _logger;

        public SchedulingService(JobService jobService, IHttpClientFactory httpClientFactory, ILogger<SchedulingService> logger)
        {
            _jobService = jobService;
            _httpClientFactory = httpClientFactory;
            _logger = logger;
        }

        public async Task ScheduleJobAsync(int jobId)
        {
            var job = await _jobService.GetJobAsync(jobId);
            if (job == null)
            {
                _logger.LogError($"Job with ID {jobId} not found");
                return;
            }

            // Calculate next execution time based on frequency
            var nextExecutionTime = CalculateNextExecutionTime(job);

            // In a real implementation, you would use a background service like Hangfire or Quartz.NET
            // For now, we'll just log the scheduling
            _logger.LogInformation($"Job {job.Name} (ID: {jobId}) scheduled to run at {nextExecutionTime}");

            // TODO: Implement actual scheduling mechanism
            // This could involve:
            // - Adding to a queue
            // - Setting up a timer
            // - Using a job scheduler like Hangfire
        }

        public async Task<JobHistory> ExecuteJobAsync(int jobId)
        {
            var job = await _jobService.GetJobAsync(jobId);
            if (job == null)
            {
                throw new ArgumentException($"Job with ID {jobId} not found");
            }

            var jobHistory = new JobHistory
            {
                JobId = jobId,
                ExecutionTime = DateTime.UtcNow,
                Success = false,
                Log = ""
            };

            try
            {
                _logger.LogInformation($"Executing job {job.Name} (ID: {jobId}) of type {job.JobType}");

                switch (job.JobType)
                {
                    case JobType.ApiEndpoint:
                        await ExecuteApiEndpointJob(job, jobHistory);
                        break;
                    case JobType.ExeFile:
                        await ExecuteExeFileJob(job, jobHistory);
                        break;
                    case JobType.DynamicCode:
                        await ExecuteDynamicCodeJob(job, jobHistory);
                        break;
                    default:
                        throw new NotSupportedException($"Job type {job.JobType} is not supported");
                }

                jobHistory.Success = true;
                _logger.LogInformation($"Job {job.Name} (ID: {jobId}) executed successfully");
            }
            catch (Exception ex)
            {
                jobHistory.Success = false;
                jobHistory.Log += $"\nError: {ex.Message}";
                _logger.LogError(ex, $"Error executing job {job.Name} (ID: {jobId})");
            }

            // Save execution history
            await _jobService.AddJobHistoryAsync(jobHistory);
            return jobHistory;
        }

        private DateTime CalculateNextExecutionTime(Job job)
        {
            var now = DateTime.UtcNow;

            return job.JobFrequency switch
            {
                JobFrequency.Once => job.StartTime,
                JobFrequency.Hourly => now.AddHours(1),
                JobFrequency.Daily => now.AddDays(1),
                JobFrequency.Weekly => now.AddDays(7),
                JobFrequency.Monthly => now.AddMonths(1),
                _ => now.AddHours(1)
            };
        }

        private async Task ExecuteApiEndpointJob(Job job, JobHistory jobHistory)
        {
            if (string.IsNullOrEmpty(job.ApiEndpointAddress))
            {
                throw new ArgumentException("API endpoint address is required for API endpoint jobs");
            }

            using var httpClient = _httpClientFactory.CreateClient();

            // Set timeout
            httpClient.Timeout = TimeSpan.FromMinutes(5);

            var response = await httpClient.GetAsync(job.ApiEndpointAddress);
            var content = await response.Content.ReadAsStringAsync();

            jobHistory.Log = $"HTTP {response.StatusCode}: {content}";

            if (!response.IsSuccessStatusCode)
            {
                throw new HttpRequestException($"API call failed with status {response.StatusCode}: {content}");
            }
        }

        private async Task ExecuteExeFileJob(Job job, JobHistory jobHistory)
        {
            if (string.IsNullOrEmpty(job.ExeFilePath))
            {
                throw new ArgumentException("Executable file path is required for executable jobs");
            }

            if (!File.Exists(job.ExeFilePath))
            {
                throw new FileNotFoundException($"Executable file not found: {job.ExeFilePath}");
            }

            var processStartInfo = new ProcessStartInfo
            {
                FileName = job.ExeFilePath,
                Arguments = job.Parameters ?? "",
                UseShellExecute = false,
                RedirectStandardOutput = true,
                RedirectStandardError = true,
                CreateNoWindow = true
            };

            using var process = new Process { StartInfo = processStartInfo };

            var outputBuilder = new StringBuilder();
            var errorBuilder = new StringBuilder();

            process.OutputDataReceived += (sender, e) =>
            {
                if (!string.IsNullOrEmpty(e.Data))
                    outputBuilder.AppendLine(e.Data);
            };

            process.ErrorDataReceived += (sender, e) =>
            {
                if (!string.IsNullOrEmpty(e.Data))
                    errorBuilder.AppendLine(e.Data);
            };

            process.Start();
            process.BeginOutputReadLine();
            process.BeginErrorReadLine();

            // Wait for the process to complete with a timeout
            var completed = await Task.Run(() => process.WaitForExit(300000)); // 5 minutes timeout

            if (!completed)
            {
                process.Kill();
                throw new TimeoutException("Process execution timed out after 5 minutes");
            }

            var output = outputBuilder.ToString();
            var error = errorBuilder.ToString();

            jobHistory.Log = $"Exit Code: {process.ExitCode}\nOutput:\n{output}\nError:\n{error}";

            if (process.ExitCode != 0)
            {
                throw new InvalidOperationException($"Process exited with code {process.ExitCode}. Error: {error}");
            }
        }

        private async Task ExecuteDynamicCodeJob(Job job, JobHistory jobHistory)
        {
            if (string.IsNullOrEmpty(job.DynamicCode))
            {
                throw new ArgumentException("Dynamic code is required for dynamic code jobs");
            }

            try
            {
                var scriptOptions = ScriptOptions.Default
                    .WithReferences(typeof(Console).Assembly)
                    .WithImports("System", "System.Console", "System.Threading.Tasks");

                var result = await CSharpScript.EvaluateAsync(job.DynamicCode, scriptOptions);

                jobHistory.Log = $"Code executed successfully. Result: {result?.ToString() ?? "null"}";
            }
            catch (CompilationErrorException ex)
            {
                var errors = string.Join("\n", ex.Diagnostics.Select(d => d.ToString()));
                jobHistory.Log = $"Compilation errors:\n{errors}";
                throw new InvalidOperationException($"Code compilation failed: {errors}");
            }
            catch (Exception ex)
            {
                jobHistory.Log = $"Runtime error: {ex.Message}";
                throw;
            }
        }
    }
}
