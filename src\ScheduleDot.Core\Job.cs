
using System;
using System.ComponentModel.DataAnnotations;

namespace ScheduleDot.Core
{
    public class Job
    {
        public int Id { get; set; }

        [Required(ErrorMessage = "Job name is required")]
        [StringLength(200, ErrorMessage = "Job name cannot exceed 200 characters")]
        public string Name { get; set; } = string.Empty;

        [Required(ErrorMessage = "Job type is required")]
        public JobType JobType { get; set; }

        [Required(ErrorMessage = "Job frequency is required")]
        public JobFrequency JobFrequency { get; set; }

        public DateTime StartTime { get; set; }
        public DateTime EndTime { get; set; }

        [StringLength(1000, ErrorMessage = "Parameters cannot exceed 1000 characters")]
        public string? Parameters { get; set; }

        [StringLength(500, ErrorMessage = "Description cannot exceed 500 characters")]
        public string? Description { get; set; }

        public JobStatus Status { get; set; }

        [Range(1, 10, ErrorMessage = "Priority must be between 1 and 10")]
        public int Priority { get; set; } = 5;

        [Range(0, 10, ErrorMessage = "Retry count must be between 0 and 10")]
        public int RetryCount { get; set; } = 3;

        [Range(0, 3600, ErrorMessage = "Retry delay must be between 0 and 3600 seconds")]
        public int RetryDelay { get; set; } = 60;

        public bool EmailNotification { get; set; }

        [Url(ErrorMessage = "Please enter a valid URL")]
        [StringLength(500, ErrorMessage = "API endpoint address cannot exceed 500 characters")]
        public string? ApiEndpointAddress { get; set; }

        [StringLength(500, ErrorMessage = "Executable file path cannot exceed 500 characters")]
        public string? ExeFilePath { get; set; }

        [StringLength(10000, ErrorMessage = "Dynamic code cannot exceed 10000 characters")]
        public string? DynamicCode { get; set; }
    }
}
