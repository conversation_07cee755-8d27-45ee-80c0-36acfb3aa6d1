
using System;

namespace ScheduleDot.Core
{
    public class Job
    {
        public int Id { get; set; }
        public string Name { get; set; }
        public JobType JobType { get; set; }
        public JobFrequency JobFrequency { get; set; }
        public DateTime StartTime { get; set; }
        public DateTime EndTime { get; set; }
        public string Parameters { get; set; }
        public string Description { get; set; }
        public JobStatus Status { get; set; }
        public int Priority { get; set; }
        public int RetryCount { get; set; }
        public int RetryDelay { get; set; }
        public bool EmailNotification { get; set; }
        public string ApiEndpointAddress { get; set; }
        public string ExeFilePath { get; set; }
        public string DynamicCode { get; set; }
    }
}
