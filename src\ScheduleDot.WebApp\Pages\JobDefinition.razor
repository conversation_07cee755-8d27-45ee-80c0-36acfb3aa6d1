
@page "/job-definition"
@page "/job-definition/{JobId:int?}"
@inject IJSRuntime JSRuntime

<PageTitle>Job Definition</PageTitle>

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <h3>@(JobId.HasValue ? "Edit Job" : "Create New Job")</h3>

            @if (!string.IsNullOrEmpty(errorMessage))
            {
                <div class="alert alert-danger" role="alert">
                    @errorMessage
                </div>
            }

            @if (!string.IsNullOrEmpty(successMessage))
            {
                <div class="alert alert-success" role="alert">
                    @successMessage
                </div>
            }

            <EditForm Model="@currentJob" OnValidSubmit="@HandleValidSubmit">
                <DataAnnotationsValidator />
                <ValidationSummary />

                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="name" class="form-label">Job Name *</label>
                            <InputText id="name" class="form-control" @bind-Value="currentJob.Name" placeholder="Enter job name" />
                            <ValidationMessage For="@(() => currentJob.Name)" />
                        </div>

                        <div class="mb-3">
                            <label for="jobType" class="form-label">Job Type *</label>
                            <InputSelect id="jobType" class="form-select" @bind-Value="currentJob.JobType" @bind-Value:after="OnJobTypeChanged">
                                <option value="">Select job type</option>
                                @foreach (var jobType in Enum.GetValues<JobType>())
                                {
                                    <option value="@jobType">@GetJobTypeDisplayName(jobType)</option>
                                }
                            </InputSelect>
                            <ValidationMessage For="@(() => currentJob.JobType)" />
                        </div>

                        <div class="mb-3">
                            <label for="frequency" class="form-label">Frequency *</label>
                            <InputSelect id="frequency" class="form-select" @bind-Value="currentJob.JobFrequency">
                                <option value="">Select frequency</option>
                                @foreach (var frequency in Enum.GetValues<JobFrequency>())
                                {
                                    <option value="@frequency">@frequency</option>
                                }
                            </InputSelect>
                            <ValidationMessage For="@(() => currentJob.JobFrequency)" />
                        </div>

                        <div class="mb-3">
                            <label for="startTime" class="form-label">Start Time</label>
                            <InputDate id="startTime" class="form-control" @bind-Value="currentJob.StartTime" />
                            <ValidationMessage For="@(() => currentJob.StartTime)" />
                        </div>

                        <div class="mb-3">
                            <label for="endTime" class="form-label">End Time</label>
                            <InputDate id="endTime" class="form-control" @bind-Value="currentJob.EndTime" />
                            <ValidationMessage For="@(() => currentJob.EndTime)" />
                        </div>

                        <div class="mb-3">
                            <label for="status" class="form-label">Status</label>
                            <InputSelect id="status" class="form-select" @bind-Value="currentJob.Status">
                                @foreach (var status in Enum.GetValues<JobStatus>())
                                {
                                    <option value="@status">@status</option>
                                }
                            </InputSelect>
                            <ValidationMessage For="@(() => currentJob.Status)" />
                        </div>
                    </div>

                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="priority" class="form-label">Priority</label>
                            <InputNumber id="priority" class="form-control" @bind-Value="currentJob.Priority" min="1" max="10" />
                            <small class="form-text text-muted">Priority from 1 (lowest) to 10 (highest)</small>
                            <ValidationMessage For="@(() => currentJob.Priority)" />
                        </div>

                        <div class="mb-3">
                            <label for="retryCount" class="form-label">Retry Count</label>
                            <InputNumber id="retryCount" class="form-control" @bind-Value="currentJob.RetryCount" min="0" max="10" />
                            <ValidationMessage For="@(() => currentJob.RetryCount)" />
                        </div>

                        <div class="mb-3">
                            <label for="retryDelay" class="form-label">Retry Delay (seconds)</label>
                            <InputNumber id="retryDelay" class="form-control" @bind-Value="currentJob.RetryDelay" min="0" />
                            <ValidationMessage For="@(() => currentJob.RetryDelay)" />
                        </div>

                        <div class="mb-3">
                            <div class="form-check">
                                <InputCheckbox id="emailNotification" class="form-check-input" @bind-Value="currentJob.EmailNotification" />
                                <label class="form-check-label" for="emailNotification">
                                    Enable Email Notifications
                                </label>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="parameters" class="form-label">Parameters</label>
                            <InputTextArea id="parameters" class="form-control" @bind-Value="currentJob.Parameters" rows="3" placeholder="Enter job parameters" />
                            <ValidationMessage For="@(() => currentJob.Parameters)" />
                        </div>

                        <div class="mb-3">
                            <label for="description" class="form-label">Description</label>
                            <InputTextArea id="description" class="form-control" @bind-Value="currentJob.Description" rows="3" placeholder="Enter job description" />
                            <ValidationMessage For="@(() => currentJob.Description)" />
                        </div>
                    </div>
                </div>

                @* Job Type Specific Fields *@
                @if (currentJob.JobType == JobType.ApiEndpoint)
                {
                    <div class="row">
                        <div class="col-12">
                            <div class="mb-3">
                                <label for="apiEndpoint" class="form-label">API Endpoint Address *</label>
                                <InputText id="apiEndpoint" class="form-control" @bind-Value="currentJob.ApiEndpointAddress" placeholder="https://api.example.com/endpoint" />
                                <ValidationMessage For="@(() => currentJob.ApiEndpointAddress)" />
                            </div>
                        </div>
                    </div>
                }

                @if (currentJob.JobType == JobType.ExeFile)
                {
                    <div class="row">
                        <div class="col-12">
                            <div class="mb-3">
                                <label for="exeFilePath" class="form-label">Executable File Path *</label>
                                <InputText id="exeFilePath" class="form-control" @bind-Value="currentJob.ExeFilePath" placeholder="C:\path\to\executable.exe" />
                                <ValidationMessage For="@(() => currentJob.ExeFilePath)" />
                            </div>
                        </div>
                    </div>
                }

                @if (currentJob.JobType == JobType.DynamicCode)
                {
                    <div class="row">
                        <div class="col-12">
                            <div class="mb-3">
                                <label for="dynamicCode" class="form-label">Dynamic Code *</label>
                                <InputTextArea id="dynamicCode" class="form-control" @bind-Value="currentJob.DynamicCode" rows="10" placeholder="Enter C# code to execute" />
                                <small class="form-text text-muted">Enter C# code that will be executed dynamically</small>
                                <ValidationMessage For="@(() => currentJob.DynamicCode)" />
                            </div>
                        </div>
                    </div>
                }

                <div class="row">
                    <div class="col-12">
                        <div class="d-flex gap-2">
                            <button type="submit" class="btn btn-primary" disabled="@isSubmitting">
                                @if (isSubmitting)
                                {
                                    <span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>
                                    <span>Saving...</span>
                                }
                                else
                                {
                                    <span>@(JobId.HasValue ? "Update Job" : "Create Job")</span>
                                }
                            </button>
                            <button type="button" class="btn btn-secondary" @onclick="ResetForm">Reset</button>
                            <a href="/job-history" class="btn btn-outline-secondary">Cancel</a>
                        </div>
                    </div>
                </div>
            </EditForm>
        </div>
    </div>
</div>

@code {
    [Parameter] public int? JobId { get; set; }

    private Job currentJob = new Job
    {
        StartTime = DateTime.Now,
        EndTime = DateTime.Now.AddDays(30),
        Status = JobStatus.Draft,
        Priority = 5,
        RetryCount = 3,
        RetryDelay = 60
    };

    private string errorMessage = string.Empty;
    private string successMessage = string.Empty;
    private bool isSubmitting = false;

    protected override async Task OnInitializedAsync()
    {
        if (JobId.HasValue)
        {
            await LoadJob();
        }
    }

    private async Task LoadJob()
    {
        try
        {
            // TODO: Load job from API
            // For now, we'll just show a placeholder
            errorMessage = "Job loading not implemented yet - API integration needed";
        }
        catch (Exception ex)
        {
            errorMessage = $"Error loading job: {ex.Message}";
        }
    }

    private async Task HandleValidSubmit()
    {
        isSubmitting = true;
        errorMessage = string.Empty;
        successMessage = string.Empty;

        try
        {
            // Validate job type specific fields
            if (!ValidateJobTypeSpecificFields())
            {
                return;
            }

            // TODO: Save job via API
            // For now, we'll just show a success message
            successMessage = JobId.HasValue ? "Job updated successfully!" : "Job created successfully!";

            // Reset form for new job
            if (!JobId.HasValue)
            {
                await Task.Delay(2000); // Show success message for 2 seconds
                ResetForm();
            }
        }
        catch (Exception ex)
        {
            errorMessage = $"Error saving job: {ex.Message}";
        }
        finally
        {
            isSubmitting = false;
        }
    }

    private bool ValidateJobTypeSpecificFields()
    {
        switch (currentJob.JobType)
        {
            case JobType.ApiEndpoint:
                if (string.IsNullOrWhiteSpace(currentJob.ApiEndpointAddress))
                {
                    errorMessage = "API Endpoint Address is required for API Endpoint jobs.";
                    return false;
                }
                if (!Uri.TryCreate(currentJob.ApiEndpointAddress, UriKind.Absolute, out _))
                {
                    errorMessage = "Please enter a valid URL for the API Endpoint Address.";
                    return false;
                }
                break;

            case JobType.ExeFile:
                if (string.IsNullOrWhiteSpace(currentJob.ExeFilePath))
                {
                    errorMessage = "Executable File Path is required for Executable File jobs.";
                    return false;
                }
                break;

            case JobType.DynamicCode:
                if (string.IsNullOrWhiteSpace(currentJob.DynamicCode))
                {
                    errorMessage = "Dynamic Code is required for Dynamic Code jobs.";
                    return false;
                }
                break;
        }

        return true;
    }

    private void OnJobTypeChanged()
    {
        // Clear job type specific fields when type changes
        currentJob.ApiEndpointAddress = string.Empty;
        currentJob.ExeFilePath = string.Empty;
        currentJob.DynamicCode = string.Empty;

        errorMessage = string.Empty;
        successMessage = string.Empty;
    }

    private void ResetForm()
    {
        currentJob = new Job
        {
            StartTime = DateTime.Now,
            EndTime = DateTime.Now.AddDays(30),
            Status = JobStatus.Draft,
            Priority = 5,
            RetryCount = 3,
            RetryDelay = 60
        };

        errorMessage = string.Empty;
        successMessage = string.Empty;
    }

    private string GetJobTypeDisplayName(JobType jobType)
    {
        return jobType switch
        {
            JobType.ApiEndpoint => "API Endpoint",
            JobType.ExeFile => "Executable File",
            JobType.DynamicCode => "Dynamic Code",
            _ => jobType.ToString()
        };
    }
}
