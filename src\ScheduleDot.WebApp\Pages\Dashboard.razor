@page "/"
@inject IJSRuntime JSRuntime
@inject NavigationManager Navigation

<PageTitle>ScheduleDot Dashboard</PageTitle>

<div class="container-fluid">
    <!-- Dashboard Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="dashboard-header text-center py-4">
                <h1 class="display-4 text-white mb-2">
                    <i class="fas fa-tachometer-alt me-3"></i>ScheduleDot Dashboard
                </h1>
                <p class="lead text-white-50 mb-0">Monitor and manage your scheduled jobs</p>
            </div>
        </div>
    </div>

    @if (isLoading)
    {
        <div class="d-flex justify-content-center align-items-center" style="min-height: 400px;">
            <div class="text-center">
                <div class="spinner-border text-primary" role="status" style="width: 3rem; height: 3rem;">
                    <span class="visually-hidden">Loading dashboard...</span>
                </div>
                <p class="mt-3 text-muted">Loading dashboard data...</p>
            </div>
        </div>
    }
    else
    {
        <!-- Statistics Cards -->
        <div class="row mb-4">
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="card stat-card bg-primary text-white h-100">
                    <div class="card-body d-flex align-items-center">
                        <div class="flex-grow-1">
                            <h5 class="card-title">
                                <i class="fas fa-tasks me-2"></i>Published Jobs
                            </h5>
                            <h2 class="mb-0">@publishedJobsCount</h2>
                            <small class="opacity-75">Active jobs ready to run</small>
                        </div>
                        <div class="stat-icon">
                            <i class="fas fa-check-circle fa-2x opacity-50"></i>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-lg-3 col-md-6 mb-3">
                <div class="card stat-card bg-success text-white h-100">
                    <div class="card-body d-flex align-items-center">
                        <div class="flex-grow-1">
                            <h5 class="card-title">
                                <i class="fas fa-play me-2"></i>Runs (24h)
                            </h5>
                            <h2 class="mb-0">@runsLast24Hours</h2>
                            <small class="opacity-75">Executions in last 24 hours</small>
                        </div>
                        <div class="stat-icon">
                            <i class="fas fa-calendar-day fa-2x opacity-50"></i>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-lg-3 col-md-6 mb-3">
                <div class="card stat-card bg-info text-white h-100">
                    <div class="card-body d-flex align-items-center">
                        <div class="flex-grow-1">
                            <h5 class="card-title">
                                <i class="fas fa-clock me-2"></i>Runs (1h)
                            </h5>
                            <h2 class="mb-0">@runsLastHour</h2>
                            <small class="opacity-75">Executions in last hour</small>
                        </div>
                        <div class="stat-icon">
                            <i class="fas fa-hourglass-half fa-2x opacity-50"></i>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-lg-3 col-md-6 mb-3">
                <div class="card stat-card bg-warning text-dark h-100">
                    <div class="card-body d-flex align-items-center">
                        <div class="flex-grow-1">
                            <h5 class="card-title">
                                <i class="fas fa-spinner me-2"></i>Running Now
                            </h5>
                            <h2 class="mb-0">@currentlyRunningCount</h2>
                            <small class="opacity-75">Jobs currently executing</small>
                        </div>
                        <div class="stat-icon">
                            <i class="fas fa-cog fa-2x opacity-50 fa-spin"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header bg-light">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-bolt me-2"></i>Quick Actions
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-3 mb-2">
                                <a href="/job-definition" class="btn btn-primary w-100">
                                    <i class="fas fa-plus me-2"></i>Create New Job
                                </a>
                            </div>
                            <div class="col-md-3 mb-2">
                                <a href="/job-history" class="btn btn-outline-secondary w-100">
                                    <i class="fas fa-history me-2"></i>View All Jobs
                                </a>
                            </div>
                            <div class="col-md-3 mb-2">
                                <button class="btn btn-outline-success w-100" @onclick="RefreshDashboard" disabled="@isRefreshing">
                                    @if (isRefreshing)
                                    {
                                        <span class="spinner-border spinner-border-sm me-2"></span>
                                    }
                                    else
                                    {
                                        <i class="fas fa-sync-alt me-2"></i>
                                    }
                                    Refresh Data
                                </button>
                            </div>
                            <div class="col-md-3 mb-2">
                                <button class="btn btn-outline-info w-100" @onclick="ToggleAutoRefresh">
                                    <i class="fas fa-@(autoRefreshEnabled ? "pause" : "play") me-2"></i>
                                    @(autoRefreshEnabled ? "Pause" : "Enable") Auto-refresh
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Published Jobs List -->
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header bg-light d-flex justify-content-between align-items-center">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-list me-2"></i>Published Jobs
                        </h5>
                        <div class="d-flex gap-2">
                            <select class="form-select form-select-sm" style="width: auto;" @bind="jobTypeFilter" @bind:after="ApplyFilters">
                                <option value="">All Types</option>
                                @foreach (var type in Enum.GetValues<JobType>())
                                {
                                    <option value="@type">@GetJobTypeDisplayName(type)</option>
                                }
                            </select>
                            <button class="btn btn-sm btn-outline-secondary" @onclick="RefreshJobs" disabled="@isRefreshing">
                                <i class="fas fa-sync-alt"></i>
                            </button>
                        </div>
                    </div>
                    <div class="card-body">
                        @if (filteredJobs == null || !filteredJobs.Any())
                        {
                            <div class="text-center py-5">
                                <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                                <h5 class="text-muted">No Published Jobs</h5>
                                <p class="text-muted">Create your first job to get started with scheduling.</p>
                                <a href="/job-definition" class="btn btn-primary">
                                    <i class="fas fa-plus me-2"></i>Create Job
                                </a>
                            </div>
                        }
                        else
                        {
                            <div class="row">
                                @foreach (var job in filteredJobs.Take(displayedJobsCount))
                                {
                                    <div class="col-lg-6 col-xl-4 mb-3">
                                        <div class="card job-card h-100" @onclick="() => NavigateToJobDetails(job.Id)" style="cursor: pointer;">
                                            <div class="card-body">
                                                <div class="d-flex justify-content-between align-items-start mb-2">
                                                    <h6 class="card-title mb-0 text-truncate" title="@job.Name">@job.Name</h6>
                                                    <span class="badge @GetJobTypeBadgeClass(job.JobType) ms-2">
                                                        <i class="@GetJobTypeIcon(job.JobType) me-1"></i>
                                                        @GetJobTypeDisplayName(job.JobType)
                                                    </span>
                                                </div>
                                                
                                                @if (!string.IsNullOrEmpty(job.Description))
                                                {
                                                    <p class="card-text text-muted small mb-2" style="display: -webkit-box; -webkit-line-clamp: 2; -webkit-box-orient: vertical; overflow: hidden;">
                                                        @job.Description
                                                    </p>
                                                }

                                                <div class="row g-2 mb-2">
                                                    <div class="col-6">
                                                        <small class="text-muted">
                                                            <i class="fas fa-clock me-1"></i>@job.JobFrequency
                                                        </small>
                                                    </div>
                                                    <div class="col-6">
                                                        <small class="text-muted">
                                                            <i class="fas fa-star me-1"></i>Priority @job.Priority
                                                        </small>
                                                    </div>
                                                </div>

                                                <div class="row g-2 mb-3">
                                                    <div class="col-12">
                                                        @if (GetLastExecution(job.Id) != null)
                                                        {
                                                            var lastExec = GetLastExecution(job.Id)!;
                                                            <small class="@(lastExec.Success ? "text-success" : "text-danger")">
                                                                <i class="fas fa-@(lastExec.Success ? "check" : "times")-circle me-1"></i>
                                                                Last run: @lastExec.ExecutionTime.ToString("MMM dd, HH:mm")
                                                            </small>
                                                        }
                                                        else
                                                        {
                                                            <small class="text-muted">
                                                                <i class="fas fa-clock me-1"></i>Never executed
                                                            </small>
                                                        }
                                                    </div>
                                                </div>

                                                <div class="d-flex gap-1">
                                                    <button class="btn btn-sm btn-outline-primary flex-fill" @onclick:stopPropagation="true" @onclick="() => NavigateToJobDetails(job.Id)">
                                                        <i class="fas fa-eye me-1"></i>View
                                                    </button>
                                                    <button class="btn btn-sm btn-outline-success" @onclick:stopPropagation="true" @onclick="() => RunJob(job.Id)" disabled="@IsJobRunning(job.Id)">
                                                        @if (IsJobRunning(job.Id))
                                                        {
                                                            <span class="spinner-border spinner-border-sm"></span>
                                                        }
                                                        else
                                                        {
                                                            <i class="fas fa-play"></i>
                                                        }
                                                    </button>
                                                    <button class="btn btn-sm btn-outline-secondary" @onclick:stopPropagation="true" @onclick="() => NavigateToJobEdit(job.Id)">
                                                        <i class="fas fa-edit"></i>
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                }
                            </div>

                            @if (filteredJobs.Count > displayedJobsCount)
                            {
                                <div class="text-center mt-3">
                                    <button class="btn btn-outline-secondary" @onclick="LoadMoreJobs">
                                        <i class="fas fa-chevron-down me-2"></i>
                                        Show More (@(filteredJobs.Count - displayedJobsCount) remaining)
                                    </button>
                                </div>
                            }
                        }
                    </div>
                </div>
            </div>
        </div>
    }
</div>

@implements IDisposable

@code {
    // Dashboard data
    private List<Job> publishedJobs = new();
    private List<Job> filteredJobs = new();
    private List<JobHistory> recentExecutions = new();
    
    // Statistics
    private int publishedJobsCount = 0;
    private int runsLast24Hours = 0;
    private int runsLastHour = 0;
    private int currentlyRunningCount = 0;
    
    // UI state
    private bool isLoading = true;
    private bool isRefreshing = false;
    private bool autoRefreshEnabled = false;
    private int displayedJobsCount = 6;
    private JobType? jobTypeFilter = null;
    private HashSet<int> runningJobs = new();
    
    // Auto-refresh timer
    private Timer? autoRefreshTimer;

    protected override async Task OnInitializedAsync()
    {
        await LoadDashboardData();
        StartAutoRefresh();
    }

    public void Dispose()
    {
        autoRefreshTimer?.Dispose();
    }

    private async Task LoadDashboardData()
    {
        isLoading = true;

        try
        {
            // TODO: Replace with actual API calls
            await LoadPublishedJobs();
            await LoadExecutionStatistics();
            ApplyFilters();
        }
        catch (Exception ex)
        {
            await JSRuntime.InvokeVoidAsync("showToast", $"Error loading dashboard: {ex.Message}", "error");
        }
        finally
        {
            isLoading = false;
        }
    }

    private async Task LoadPublishedJobs()
    {
        // TODO: Replace with actual API call
        publishedJobs = GenerateSamplePublishedJobs();
        publishedJobsCount = publishedJobs.Count;
        await Task.CompletedTask;
    }

    private async Task LoadExecutionStatistics()
    {
        // TODO: Replace with actual API calls
        recentExecutions = GenerateSampleExecutions();

        var now = DateTime.Now;
        var last24Hours = now.AddHours(-24);
        var lastHour = now.AddHours(-1);

        runsLast24Hours = recentExecutions.Count(e => e.ExecutionTime >= last24Hours);
        runsLastHour = recentExecutions.Count(e => e.ExecutionTime >= lastHour);
        currentlyRunningCount = runningJobs.Count;

        await Task.CompletedTask;
    }

    private void ApplyFilters()
    {
        var filtered = publishedJobs.AsEnumerable();

        if (jobTypeFilter.HasValue)
        {
            filtered = filtered.Where(j => j.JobType == jobTypeFilter.Value);
        }

        filteredJobs = filtered.OrderByDescending(j => j.Priority)
                              .ThenBy(j => j.Name)
                              .ToList();

        displayedJobsCount = Math.Min(6, filteredJobs.Count);
    }

    private async Task RefreshDashboard()
    {
        isRefreshing = true;
        await LoadDashboardData();
        await JSRuntime.InvokeVoidAsync("showToast", "Dashboard refreshed successfully!", "success");
        isRefreshing = false;
    }

    private async Task RefreshJobs()
    {
        isRefreshing = true;
        await LoadPublishedJobs();
        ApplyFilters();
        isRefreshing = false;
    }

    private void ToggleAutoRefresh()
    {
        autoRefreshEnabled = !autoRefreshEnabled;
        if (autoRefreshEnabled)
        {
            StartAutoRefresh();
        }
        else
        {
            StopAutoRefresh();
        }
    }

    private void StartAutoRefresh()
    {
        if (autoRefreshEnabled)
        {
            autoRefreshTimer = new Timer(async _ =>
            {
                await InvokeAsync(async () =>
                {
                    await LoadExecutionStatistics();
                    StateHasChanged();
                });
            }, null, TimeSpan.FromSeconds(30), TimeSpan.FromSeconds(30));
        }
    }

    private void StopAutoRefresh()
    {
        autoRefreshTimer?.Dispose();
        autoRefreshTimer = null;
    }

    private void LoadMoreJobs()
    {
        displayedJobsCount = Math.Min(displayedJobsCount + 6, filteredJobs.Count);
    }

    private async Task RunJob(int jobId)
    {
        try
        {
            runningJobs.Add(jobId);
            currentlyRunningCount = runningJobs.Count;
            StateHasChanged();

            // TODO: Replace with actual API call
            await Task.Delay(3000); // Simulate job execution

            runningJobs.Remove(jobId);
            currentlyRunningCount = runningJobs.Count;

            await JSRuntime.InvokeVoidAsync("showToast", "Job started successfully!", "success");
            await LoadExecutionStatistics(); // Refresh stats
            StateHasChanged();
        }
        catch (Exception ex)
        {
            runningJobs.Remove(jobId);
            currentlyRunningCount = runningJobs.Count;
            await JSRuntime.InvokeVoidAsync("showToast", $"Error running job: {ex.Message}", "error");
        }
    }

    private void NavigateToJobDetails(int jobId)
    {
        Navigation.NavigateTo($"/job-details/{jobId}");
    }

    private void NavigateToJobEdit(int jobId)
    {
        Navigation.NavigateTo($"/job-definition/{jobId}");
    }

    private bool IsJobRunning(int jobId)
    {
        return runningJobs.Contains(jobId);
    }

    private JobHistory? GetLastExecution(int jobId)
    {
        return recentExecutions
            .Where(e => e.JobId == jobId)
            .OrderByDescending(e => e.ExecutionTime)
            .FirstOrDefault();
    }

    // Helper methods
    private string GetJobTypeDisplayName(JobType jobType)
    {
        return jobType switch
        {
            JobType.ApiEndpoint => "API",
            JobType.ExeFile => "EXE",
            JobType.DynamicCode => "Code",
            _ => jobType.ToString()
        };
    }

    private string GetJobTypeIcon(JobType jobType)
    {
        return jobType switch
        {
            JobType.ApiEndpoint => "fas fa-link",
            JobType.ExeFile => "fas fa-terminal",
            JobType.DynamicCode => "fas fa-code",
            _ => "fas fa-cog"
        };
    }

    private string GetJobTypeBadgeClass(JobType jobType)
    {
        return jobType switch
        {
            JobType.ApiEndpoint => "bg-primary",
            JobType.ExeFile => "bg-success",
            JobType.DynamicCode => "bg-warning text-dark",
            _ => "bg-secondary"
        };
    }

    // Sample data generation (TODO: Replace with actual API calls)
    private List<Job> GenerateSamplePublishedJobs()
    {
        return new List<Job>
        {
            new Job
            {
                Id = 1,
                Name = "Daily Database Backup",
                Description = "Automated backup of production database with compression and encryption to cloud storage.",
                JobType = JobType.ExeFile,
                JobFrequency = JobFrequency.Daily,
                Status = JobStatus.Published,
                Priority = 9,
                StartTime = DateTime.Now.AddDays(-30),
                EndTime = DateTime.Now.AddDays(365),
                RetryCount = 3,
                RetryDelay = 300,
                EmailNotification = true
            },
            new Job
            {
                Id = 2,
                Name = "API Health Monitor",
                Description = "Continuous monitoring of critical API endpoints for availability and performance metrics.",
                JobType = JobType.ApiEndpoint,
                JobFrequency = JobFrequency.Hourly,
                Status = JobStatus.Published,
                Priority = 10,
                StartTime = DateTime.Now.AddDays(-7),
                EndTime = DateTime.Now.AddDays(90),
                RetryCount = 2,
                RetryDelay = 60,
                EmailNotification = true
            },
            new Job
            {
                Id = 3,
                Name = "Weekly Sales Report",
                Description = "Generate comprehensive weekly sales analytics and distribute to stakeholders.",
                JobType = JobType.DynamicCode,
                JobFrequency = JobFrequency.Weekly,
                Status = JobStatus.Published,
                Priority = 7,
                StartTime = DateTime.Now.AddDays(-14),
                EndTime = DateTime.Now.AddDays(180),
                RetryCount = 1,
                RetryDelay = 120,
                EmailNotification = true
            },
            new Job
            {
                Id = 4,
                Name = "System Cleanup",
                Description = "Clean temporary files and optimize system performance.",
                JobType = JobType.ExeFile,
                JobFrequency = JobFrequency.Daily,
                Status = JobStatus.Published,
                Priority = 5,
                StartTime = DateTime.Now.AddDays(-10),
                EndTime = DateTime.Now.AddDays(365),
                RetryCount = 2,
                RetryDelay = 180,
                EmailNotification = false
            },
            new Job
            {
                Id = 5,
                Name = "Data Sync Service",
                Description = "Synchronize data between multiple systems and databases.",
                JobType = JobType.ApiEndpoint,
                JobFrequency = JobFrequency.Hourly,
                Status = JobStatus.Published,
                Priority = 8,
                StartTime = DateTime.Now.AddDays(-5),
                EndTime = DateTime.Now.AddDays(60),
                RetryCount = 3,
                RetryDelay = 90,
                EmailNotification = true
            },
            new Job
            {
                Id = 6,
                Name = "Log Analysis",
                Description = "Analyze application logs for errors and performance issues.",
                JobType = JobType.DynamicCode,
                JobFrequency = JobFrequency.Daily,
                Status = JobStatus.Published,
                Priority = 6,
                StartTime = DateTime.Now.AddDays(-20),
                EndTime = DateTime.Now.AddDays(120),
                RetryCount = 1,
                RetryDelay = 300,
                EmailNotification = false
            },
            new Job
            {
                Id = 7,
                Name = "Security Scan",
                Description = "Perform automated security scans and vulnerability assessments.",
                JobType = JobType.ExeFile,
                JobFrequency = JobFrequency.Weekly,
                Status = JobStatus.Published,
                Priority = 9,
                StartTime = DateTime.Now.AddDays(-3),
                EndTime = DateTime.Now.AddDays(90),
                RetryCount = 2,
                RetryDelay = 600,
                EmailNotification = true
            },
            new Job
            {
                Id = 8,
                Name = "Cache Refresh",
                Description = "Refresh application cache to ensure optimal performance.",
                JobType = JobType.ApiEndpoint,
                JobFrequency = JobFrequency.Hourly,
                Status = JobStatus.Published,
                Priority = 4,
                StartTime = DateTime.Now.AddDays(-1),
                EndTime = DateTime.Now.AddDays(30),
                RetryCount = 1,
                RetryDelay = 30,
                EmailNotification = false
            }
        };
    }

    private List<JobHistory> GenerateSampleExecutions()
    {
        var executions = new List<JobHistory>();
        var random = new Random();
        var now = DateTime.Now;

        // Generate executions for the last 24 hours
        for (int i = 0; i < 50; i++)
        {
            var executionTime = now.AddMinutes(-random.Next(0, 1440)); // Random time in last 24 hours
            var jobId = random.Next(1, 9); // Random job ID from our sample jobs
            var success = random.Next(0, 10) > 1; // 90% success rate

            executions.Add(new JobHistory
            {
                Id = i + 1,
                JobId = jobId,
                ExecutionTime = executionTime,
                Success = success,
                Log = success ? "Execution completed successfully" : "Execution failed with error"
            });
        }

        return executions.OrderByDescending(e => e.ExecutionTime).ToList();
    }
}
