
using Microsoft.AspNetCore.Mvc;
using ScheduleDot.Core;
using ScheduleDot.Api.Services;

namespace ScheduleDot.Api.Controllers
{
    [ApiController]
    [Route("[controller]")]
    public class JobsController : ControllerBase
    {
        private readonly JobService _jobService;

        public JobsController(JobService jobService)
        {
            _jobService = jobService;
        }

        [HttpGet]
        public async Task<ActionResult<IEnumerable<Job>>> GetJobs()
        {
            try
            {
                var jobs = await _jobService.GetJobsAsync();
                return Ok(jobs);
            }
            catch (Exception ex)
            {
                return StatusCode(500, $"Internal server error: {ex.Message}");
            }
        }

        [HttpGet("{id}")]
        public async Task<ActionResult<Job>> GetJob(int id)
        {
            try
            {
                var job = await _jobService.GetJobAsync(id);
                if (job == null)
                {
                    return NotFound($"Job with ID {id} not found.");
                }
                return Ok(job);
            }
            catch (Exception ex)
            {
                return StatusCode(500, $"Internal server error: {ex.Message}");
            }
        }

        [HttpPost]
        public async Task<ActionResult<Job>> CreateJob([FromBody] Job job)
        {
            try
            {
                if (job == null)
                {
                    return BadRequest("Job data is required.");
                }

                if (!ModelState.IsValid)
                {
                    return BadRequest(ModelState);
                }

                var createdJob = await _jobService.CreateJobAsync(job);
                return CreatedAtAction(nameof(GetJob), new { id = createdJob.Id }, createdJob);
            }
            catch (Exception ex)
            {
                return StatusCode(500, $"Internal server error: {ex.Message}");
            }
        }

        [HttpPut("{id}")]
        public async Task<ActionResult<Job>> UpdateJob(int id, [FromBody] Job job)
        {
            try
            {
                if (job == null)
                {
                    return BadRequest("Job data is required.");
                }

                if (id != job.Id)
                {
                    return BadRequest("Job ID mismatch.");
                }

                if (!ModelState.IsValid)
                {
                    return BadRequest(ModelState);
                }

                var updatedJob = await _jobService.UpdateJobAsync(id, job);
                if (updatedJob == null)
                {
                    return NotFound($"Job with ID {id} not found.");
                }

                return Ok(updatedJob);
            }
            catch (Exception ex)
            {
                return StatusCode(500, $"Internal server error: {ex.Message}");
            }
        }

        [HttpDelete("{id}")]
        public async Task<ActionResult> DeleteJob(int id)
        {
            try
            {
                var success = await _jobService.DeleteJobAsync(id);
                if (!success)
                {
                    return NotFound($"Job with ID {id} not found.");
                }

                return NoContent();
            }
            catch (Exception ex)
            {
                return StatusCode(500, $"Internal server error: {ex.Message}");
            }
        }
    }
}
