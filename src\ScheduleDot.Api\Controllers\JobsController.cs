
using Microsoft.AspNetCore.Mvc;
using ScheduleDot.Core;

namespace ScheduleDot.Api.Controllers
{
    [ApiController]
    [Route("[controller]")]
    public class JobsController : ControllerBase
    {
        [HttpGet]
        public void GetJobs() 
        {
            // Pseudocode: Get all jobs from the database
        }

        [HttpGet("{id}")]
        public void GetJob(int id)
        {
            // Pseudocode: Get a single job from the database
        }

        [HttpPost]
        public void CreateJob(Job job)
        {
            // Pseudocode: Create a new job in the database
        }

        [HttpPut("{id}")]
        public void UpdateJob(int id, Job job)
        {
            // Pseudocode: Update a job in the database
        }

        [HttpDelete("{id}")]
        public void DeleteJob(int id)
        {
            // Pseudocode: Delete a job from the database
        }
    }
}
