
@page "/job-details/{JobId:int}"
@using ScheduleDot.Core
@inject IJSRuntime JSRuntime

<PageTitle>Job Details</PageTitle>

<div class="container-fluid">
    @if (isLoading)
    {
        <div class="text-center">
            <div class="spinner-border" role="status">
                <span class="visually-hidden">Loading...</span>
            </div>
            <p class="mt-2">Loading job details...</p>
        </div>
    }
    else if (job == null)
    {
        <div class="alert alert-danger">
            <h5>Job Not Found</h5>
            <p>The job with ID @JobId was not found.</p>
            <a href="/job-history" class="btn btn-primary">Back to Job History</a>
        </div>
    }
    else
    {
        <div class="row">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <div>
                        <h3>@job.Name</h3>
                        <nav aria-label="breadcrumb">
                            <ol class="breadcrumb">
                                <li class="breadcrumb-item"><a href="/job-history">Job History</a></li>
                                <li class="breadcrumb-item active" aria-current="page">@job.Name</li>
                            </ol>
                        </nav>
                    </div>
                    <div class="btn-group" role="group">
                        <a href="/job-definition/@JobId" class="btn btn-outline-secondary">
                            <i class="fas fa-edit"></i> Edit Job
                        </a>
                        <button class="btn btn-success" @onclick="RunJob" disabled="@(job.Status != JobStatus.Published || isRunning)">
                            @if (isRunning)
                            {
                                <span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>
                                <span>Running...</span>
                            }
                            else
                            {
                                <i class="fas fa-play"></i>
                                <span>Run Now</span>
                            }
                        </button>
                    </div>
                </div>

                @if (!string.IsNullOrEmpty(errorMessage))
                {
                    <div class="alert alert-danger" role="alert">
                        @errorMessage
                    </div>
                }

                @if (!string.IsNullOrEmpty(successMessage))
                {
                    <div class="alert alert-success" role="alert">
                        @successMessage
                    </div>
                }

                <!-- Job Information Card -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="card-title mb-0">Job Information</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <table class="table table-borderless">
                                    <tr>
                                        <td class="fw-bold">Name:</td>
                                        <td>@job.Name</td>
                                    </tr>
                                    <tr>
                                        <td class="fw-bold">Type:</td>
                                        <td><span class="badge bg-info">@GetJobTypeDisplayName(job.JobType)</span></td>
                                    </tr>
                                    <tr>
                                        <td class="fw-bold">Status:</td>
                                        <td><span class="badge @GetStatusBadgeClass(job.Status)">@job.Status</span></td>
                                    </tr>
                                    <tr>
                                        <td class="fw-bold">Frequency:</td>
                                        <td>@job.JobFrequency</td>
                                    </tr>
                                    <tr>
                                        <td class="fw-bold">Priority:</td>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                @for (int i = 1; i <= 10; i++)
                                                {
                                                    <i class="fas fa-star @(i <= job.Priority ? "text-warning" : "text-muted")" style="font-size: 0.8em;"></i>
                                                }
                                                <span class="ms-2">(@job.Priority/10)</span>
                                            </div>
                                        </td>
                                    </tr>
                                </table>
                            </div>
                            <div class="col-md-6">
                                <table class="table table-borderless">
                                    <tr>
                                        <td class="fw-bold">Start Time:</td>
                                        <td>@job.StartTime.ToString("yyyy-MM-dd HH:mm")</td>
                                    </tr>
                                    <tr>
                                        <td class="fw-bold">End Time:</td>
                                        <td>@job.EndTime.ToString("yyyy-MM-dd HH:mm")</td>
                                    </tr>
                                    <tr>
                                        <td class="fw-bold">Retry Count:</td>
                                        <td>@job.RetryCount</td>
                                    </tr>
                                    <tr>
                                        <td class="fw-bold">Retry Delay:</td>
                                        <td>@job.RetryDelay seconds</td>
                                    </tr>
                                    <tr>
                                        <td class="fw-bold">Email Notifications:</td>
                                        <td>
                                            @if (job.EmailNotification)
                                            {
                                                <span class="badge bg-success">Enabled</span>
                                            }
                                            else
                                            {
                                                <span class="badge bg-secondary">Disabled</span>
                                            }
                                        </td>
                                    </tr>
                                </table>
                            </div>
                        </div>

                        @if (!string.IsNullOrEmpty(job.Description))
                        {
                            <div class="mt-3">
                                <h6>Description:</h6>
                                <p class="text-muted">@job.Description</p>
                            </div>
                        }

                        @if (!string.IsNullOrEmpty(job.Parameters))
                        {
                            <div class="mt-3">
                                <h6>Parameters:</h6>
                                <pre class="bg-light p-2 rounded"><code>@job.Parameters</code></pre>
                            </div>
                        }
                    </div>
                </div>

                <!-- Job Type Specific Information -->
                @if (job.JobType == JobType.ApiEndpoint && !string.IsNullOrEmpty(job.ApiEndpointAddress))
                {
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5 class="card-title mb-0">API Endpoint Configuration</h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-12">
                                    <label class="fw-bold">Endpoint URL:</label>
                                    <div class="input-group">
                                        <input type="text" class="form-control" value="@job.ApiEndpointAddress" readonly />
                                        <button class="btn btn-outline-secondary" @onclick="() => CopyToClipboard(job.ApiEndpointAddress)">
                                            <i class="fas fa-copy"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                }

                @if (job.JobType == JobType.ExeFile && !string.IsNullOrEmpty(job.ExeFilePath))
                {
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5 class="card-title mb-0">Executable Configuration</h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-12">
                                    <label class="fw-bold">File Path:</label>
                                    <div class="input-group">
                                        <input type="text" class="form-control" value="@job.ExeFilePath" readonly />
                                        <button class="btn btn-outline-secondary" @onclick="() => CopyToClipboard(job.ExeFilePath)">
                                            <i class="fas fa-copy"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                }

                @if (job.JobType == JobType.DynamicCode && !string.IsNullOrEmpty(job.DynamicCode))
                {
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5 class="card-title mb-0">Dynamic Code</h5>
                        </div>
                        <div class="card-body">
                            <pre class="bg-dark text-light p-3 rounded"><code>@job.DynamicCode</code></pre>
                            <button class="btn btn-outline-secondary mt-2" @onclick="() => CopyToClipboard(job.DynamicCode)">
                                <i class="fas fa-copy"></i> Copy Code
                            </button>
                        </div>
                    </div>
                }

                <!-- Execution History -->
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="card-title mb-0">Execution History</h5>
                        <button class="btn btn-outline-secondary btn-sm" @onclick="RefreshHistory">
                            <i class="fas fa-refresh"></i> Refresh
                        </button>
                    </div>
                    <div class="card-body">
                        @if (isLoadingHistory)
                        {
                            <div class="text-center">
                                <div class="spinner-border spinner-border-sm" role="status">
                                    <span class="visually-hidden">Loading...</span>
                                </div>
                                <span class="ms-2">Loading execution history...</span>
                            </div>
                        }
                        else if (executionHistory == null || !executionHistory.Any())
                        {
                            <div class="text-center text-muted">
                                <i class="fas fa-history fa-3x mb-3"></i>
                                <p>No execution history found for this job.</p>
                            </div>
                        }
                        else
                        {
                            <div class="table-responsive">
                                <table class="table table-striped">
                                    <thead>
                                        <tr>
                                            <th>Execution Time</th>
                                            <th>Status</th>
                                            <th>Duration</th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @foreach (var history in executionHistory.Take(10))
                                        {
                                            <tr>
                                                <td>@history.ExecutionTime.ToString("yyyy-MM-dd HH:mm:ss")</td>
                                                <td>
                                                    @if (history.Success)
                                                    {
                                                        <span class="badge bg-success">
                                                            <i class="fas fa-check-circle"></i> Success
                                                        </span>
                                                    }
                                                    else
                                                    {
                                                        <span class="badge bg-danger">
                                                            <i class="fas fa-times-circle"></i> Failed
                                                        </span>
                                                    }
                                                </td>
                                                <td>
                                                    @* Calculate duration - for demo purposes, we'll show a random duration *@
                                                    <small class="text-muted">@(new Random().Next(1, 300)) seconds</small>
                                                </td>
                                                <td>
                                                    <button class="btn btn-outline-info btn-sm" @onclick="() => ShowExecutionLog(history)">
                                                        <i class="fas fa-file-alt"></i> View Log
                                                    </button>
                                                </td>
                                            </tr>
                                        }
                                    </tbody>
                                </table>
                            </div>

                            @if (executionHistory.Count > 10)
                            {
                                <div class="text-center">
                                    <small class="text-muted">Showing latest 10 executions out of @executionHistory.Count total</small>
                                </div>
                            }
                        }
                    </div>
                </div>
            }
        </div>
    }
</div>

<!-- Log Modal -->
<div class="modal fade" id="logModal" tabindex="-1" aria-labelledby="logModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="logModalLabel">Execution Log</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                @if (selectedHistory != null)
                {
                    <div class="mb-3">
                        <strong>Execution Time:</strong> @selectedHistory.ExecutionTime.ToString("yyyy-MM-dd HH:mm:ss")
                    </div>
                    <div class="mb-3">
                        <strong>Status:</strong>
                        @if (selectedHistory.Success)
                        {
                            <span class="badge bg-success">Success</span>
                        }
                        else
                        {
                            <span class="badge bg-danger">Failed</span>
                        }
                    </div>
                    <div class="mb-3">
                        <strong>Log Output:</strong>
                        <pre class="bg-dark text-light p-3 rounded mt-2" style="max-height: 400px; overflow-y: auto;"><code>@(selectedHistory.Log ?? "No log output available")</code></pre>
                    </div>
                }
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-outline-secondary" @onclick="() => CopyToClipboard(selectedHistory?.Log ?? string.Empty)">
                    <i class="fas fa-copy"></i> Copy Log
                </button>
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>

@code {
    [Parameter] public int JobId { get; set; }

    private Job? job;
    private List<JobHistory> executionHistory = new();
    private JobHistory? selectedHistory;

    private string errorMessage = string.Empty;
    private string successMessage = string.Empty;
    private bool isLoading = true;
    private bool isLoadingHistory = true;
    private bool isRunning = false;

    protected override async Task OnInitializedAsync()
    {
        await LoadJobDetails();
        await LoadExecutionHistory();
    }

    private async Task LoadJobDetails()
    {
        isLoading = true;
        errorMessage = string.Empty;

        try
        {
            // TODO: Load job from API
            // For now, we'll create sample data
            job = GenerateSampleJob(JobId);
        }
        catch (Exception ex)
        {
            errorMessage = $"Error loading job details: {ex.Message}";
        }
        finally
        {
            isLoading = false;
        }
    }

    private async Task LoadExecutionHistory()
    {
        isLoadingHistory = true;

        try
        {
            // TODO: Load execution history from API
            // For now, we'll create sample data
            executionHistory = GenerateSampleExecutionHistory(JobId);
        }
        catch (Exception ex)
        {
            errorMessage = $"Error loading execution history: {ex.Message}";
        }
        finally
        {
            isLoadingHistory = false;
        }
    }

    private Job? GenerateSampleJob(int id)
    {
        var sampleJobs = new List<Job>
        {
            new Job
            {
                Id = 1,
                Name = "Daily Data Backup",
                Description = "Backup database to cloud storage every day at 2 AM",
                JobType = JobType.ExeFile,
                JobFrequency = JobFrequency.Daily,
                Status = JobStatus.Published,
                Priority = 8,
                StartTime = DateTime.Now.AddDays(-30),
                EndTime = DateTime.Now.AddDays(365),
                RetryCount = 3,
                RetryDelay = 300,
                EmailNotification = true,
                ExeFilePath = "C:\\Scripts\\backup.exe",
                Parameters = "--database=production --destination=s3://backup-bucket"
            },
            new Job
            {
                Id = 2,
                Name = "API Health Check",
                Description = "Monitor API endpoint availability every hour",
                JobType = JobType.ApiEndpoint,
                JobFrequency = JobFrequency.Hourly,
                Status = JobStatus.Published,
                Priority = 9,
                StartTime = DateTime.Now.AddDays(-7),
                EndTime = DateTime.Now.AddDays(90),
                RetryCount = 2,
                RetryDelay = 60,
                EmailNotification = true,
                ApiEndpointAddress = "https://api.example.com/health"
            },
            new Job
            {
                Id = 3,
                Name = "Generate Reports",
                Description = "Generate monthly sales reports using dynamic C# code",
                JobType = JobType.DynamicCode,
                JobFrequency = JobFrequency.Monthly,
                Status = JobStatus.Draft,
                Priority = 5,
                StartTime = DateTime.Now,
                EndTime = DateTime.Now.AddDays(365),
                RetryCount = 1,
                RetryDelay = 120,
                EmailNotification = false,
                DynamicCode = @"using System;
using System.IO;

Console.WriteLine(""Starting report generation..."");
var reportDate = DateTime.Now.ToString(""yyyy-MM"");
Console.WriteLine($""Generating report for {reportDate}"");

// Simulate report generation
await Task.Delay(2000);

Console.WriteLine(""Report generation completed successfully!"");"
            }
        };

        return sampleJobs.FirstOrDefault(j => j.Id == id);
    }

    private List<JobHistory> GenerateSampleExecutionHistory(int jobId)
    {
        var random = new Random();
        var history = new List<JobHistory>();

        for (int i = 0; i < 15; i++)
        {
            var success = random.Next(0, 10) > 1; // 90% success rate
            history.Add(new JobHistory
            {
                Id = i + 1,
                JobId = jobId,
                ExecutionTime = DateTime.Now.AddHours(-i * 2),
                Success = success,
                Log = success
                    ? $"Job executed successfully at {DateTime.Now.AddHours(-i * 2):yyyy-MM-dd HH:mm:ss}\nExecution completed without errors.\nOutput: Process completed successfully."
                    : $"Job execution failed at {DateTime.Now.AddHours(-i * 2):yyyy-MM-dd HH:mm:ss}\nError: {GetRandomError()}\nStack trace: at JobExecutor.Execute() line 42"
            });
        }

        return history.OrderByDescending(h => h.ExecutionTime).ToList();
    }

    private string GetRandomError()
    {
        var errors = new[]
        {
            "Connection timeout",
            "File not found",
            "Access denied",
            "Network unreachable",
            "Invalid configuration",
            "Memory allocation failed"
        };
        return errors[new Random().Next(errors.Length)];
    }

    private async Task RunJob()
    {
        isRunning = true;
        errorMessage = string.Empty;
        successMessage = string.Empty;

        try
        {
            // TODO: Call API to run job
            await Task.Delay(2000); // Simulate job execution
            successMessage = "Job execution started successfully!";

            // Refresh execution history after a delay
            await Task.Delay(1000);
            await LoadExecutionHistory();
        }
        catch (Exception ex)
        {
            errorMessage = $"Error running job: {ex.Message}";
        }
        finally
        {
            isRunning = false;
        }
    }

    private async Task RefreshHistory()
    {
        await LoadExecutionHistory();
    }

    private async Task ShowExecutionLog(JobHistory history)
    {
        selectedHistory = history;
        await JSRuntime.InvokeVoidAsync("eval", "new bootstrap.Modal(document.getElementById('logModal')).show()");
    }

    private async Task CopyToClipboard(string text)
    {
        try
        {
            await JSRuntime.InvokeVoidAsync("navigator.clipboard.writeText", text);
            successMessage = "Copied to clipboard!";
            await Task.Delay(2000);
            successMessage = string.Empty;
        }
        catch
        {
            // Fallback for older browsers
            await JSRuntime.InvokeVoidAsync("eval", $"prompt('Copy this text:', '{text}')");
        }
    }

    private string GetJobTypeDisplayName(JobType jobType)
    {
        return jobType switch
        {
            JobType.ApiEndpoint => "API Endpoint",
            JobType.ExeFile => "Executable File",
            JobType.DynamicCode => "Dynamic Code",
            _ => jobType.ToString()
        };
    }

    private string GetStatusBadgeClass(JobStatus status)
    {
        return status switch
        {
            JobStatus.Draft => "bg-secondary",
            JobStatus.Pending => "bg-warning",
            JobStatus.Published => "bg-success",
            _ => "bg-secondary"
        };
    }
}
