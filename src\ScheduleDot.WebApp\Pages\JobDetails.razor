
@page "/job-details/{JobId:int}"
@inject IJSRuntime JSRuntime
@inject NavigationManager Navigation

<PageTitle>Job Details</PageTitle>

<div class="container-fluid">
    @if (isLoading)
    {
        <div class="d-flex justify-content-center align-items-center" style="min-height: 400px;">
            <div class="text-center">
                <div class="spinner-border text-primary" role="status" style="width: 3rem; height: 3rem;">
                    <span class="visually-hidden">Loading...</span>
                </div>
                <p class="mt-3 text-muted">Loading job details...</p>
            </div>
        </div>
    }
    else if (job == null)
    {
        <div class="row justify-content-center">
            <div class="col-md-6">
                <div class="alert alert-danger text-center">
                    <i class="fas fa-exclamation-triangle fa-3x mb-3"></i>
                    <h4>Job Not Found</h4>
                    <p>The job with ID @JobId could not be found.</p>
                    <a href="/job-history" class="btn btn-primary">
                        <i class="fas fa-arrow-left"></i> Back to Job History
                    </a>
                </div>
            </div>
        </div>
    }
    else
    {
        <!-- Header Section -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-start">
                    <div>
                        <nav aria-label="breadcrumb">
                            <ol class="breadcrumb">
                                <li class="breadcrumb-item">
                                    <a href="/job-history" class="text-decoration-none">
                                        <i class="fas fa-list"></i> Job History
                                    </a>
                                </li>
                                <li class="breadcrumb-item active" aria-current="page">@job.Name</li>
                            </ol>
                        </nav>
                        <div class="d-flex align-items-center gap-3">
                            <h2 class="mb-0">@job.Name</h2>
                            <span class="badge @GetStatusBadgeClass(job.Status) fs-6">@job.Status</span>
                        </div>
                        @if (!string.IsNullOrEmpty(job.Description))
                        {
                            <p class="text-muted mt-2 mb-0">@job.Description</p>
                        }
                    </div>
                    <div class="d-flex gap-2">
                        <a href="/job-definition/@JobId" class="btn btn-outline-secondary">
                            <i class="fas fa-edit"></i> Edit
                        </a>
                        <button class="btn btn-success" @onclick="RunJob" disabled="@(job.Status != JobStatus.Published || isRunning)">
                            @if (isRunning)
                            {
                                <span class="spinner-border spinner-border-sm me-2" role="status"></span>
                                <span>Running...</span>
                            }
                            else
                            {
                                <i class="fas fa-play me-2"></i>
                                <span>Run Now</span>
                            }
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Alert Messages -->
        @if (!string.IsNullOrEmpty(errorMessage))
        {
            <div class="alert alert-danger alert-dismissible fade show" role="alert">
                <i class="fas fa-exclamation-circle me-2"></i>
                @errorMessage
                <button type="button" class="btn-close" @onclick="() => errorMessage = string.Empty"></button>
            </div>
        }

        @if (!string.IsNullOrEmpty(successMessage))
        {
            <div class="alert alert-success alert-dismissible fade show" role="alert">
                <i class="fas fa-check-circle me-2"></i>
                @successMessage
                <button type="button" class="btn-close" @onclick="() => successMessage = string.Empty"></button>
            </div>
        }

        <!-- Job Information Cards -->
        <div class="row">
            <!-- Basic Information Card -->
            <div class="col-lg-6 mb-4">
                <div class="card h-100">
                    <div class="card-header bg-primary text-white">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-info-circle me-2"></i>Basic Information
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row g-3">
                            <div class="col-sm-6">
                                <label class="form-label fw-bold text-muted">Job Type</label>
                                <div class="d-flex align-items-center">
                                    <i class="@GetJobTypeIcon(job.JobType) me-2 text-info"></i>
                                    <span class="badge bg-info">@GetJobTypeDisplayName(job.JobType)</span>
                                </div>
                            </div>
                            <div class="col-sm-6">
                                <label class="form-label fw-bold text-muted">Frequency</label>
                                <div class="d-flex align-items-center">
                                    <i class="fas fa-clock me-2 text-secondary"></i>
                                    <span>@job.JobFrequency</span>
                                </div>
                            </div>
                            <div class="col-sm-6">
                                <label class="form-label fw-bold text-muted">Priority</label>
                                <div class="d-flex align-items-center">
                                    @for (int i = 1; i <= 5; i++)
                                    {
                                        <i class="fas fa-star @(i <= (job.Priority / 2.0) ? "text-warning" : "text-muted") me-1"></i>
                                    }
                                    <span class="ms-2 small">(@job.Priority/10)</span>
                                </div>
                            </div>
                            <div class="col-sm-6">
                                <label class="form-label fw-bold text-muted">Email Notifications</label>
                                <div class="d-flex align-items-center">
                                    @if (job.EmailNotification)
                                    {
                                        <i class="fas fa-bell text-success me-2"></i>
                                        <span class="badge bg-success">Enabled</span>
                                    }
                                    else
                                    {
                                        <i class="fas fa-bell-slash text-muted me-2"></i>
                                        <span class="badge bg-secondary">Disabled</span>
                                    }
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Schedule Information Card -->
            <div class="col-lg-6 mb-4">
                <div class="card h-100">
                    <div class="card-header bg-success text-white">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-calendar-alt me-2"></i>Schedule Information
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row g-3">
                            <div class="col-sm-6">
                                <label class="form-label fw-bold text-muted">Start Time</label>
                                <div class="d-flex align-items-center">
                                    <i class="fas fa-play-circle me-2 text-success"></i>
                                    <span>@job.StartTime.ToString("MMM dd, yyyy HH:mm")</span>
                                </div>
                            </div>
                            <div class="col-sm-6">
                                <label class="form-label fw-bold text-muted">End Time</label>
                                <div class="d-flex align-items-center">
                                    <i class="fas fa-stop-circle me-2 text-danger"></i>
                                    <span>@job.EndTime.ToString("MMM dd, yyyy HH:mm")</span>
                                </div>
                            </div>
                            <div class="col-sm-6">
                                <label class="form-label fw-bold text-muted">Retry Count</label>
                                <div class="d-flex align-items-center">
                                    <i class="fas fa-redo me-2 text-warning"></i>
                                    <span>@job.RetryCount attempts</span>
                                </div>
                            </div>
                            <div class="col-sm-6">
                                <label class="form-label fw-bold text-muted">Retry Delay</label>
                                <div class="d-flex align-items-center">
                                    <i class="fas fa-hourglass-half me-2 text-info"></i>
                                    <span>@job.RetryDelay seconds</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Job Configuration Section -->
        @if (!string.IsNullOrEmpty(job.Parameters) || HasJobTypeSpecificConfig())
        {
            <div class="row mb-4">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header bg-info text-white">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-cogs me-2"></i>Configuration
                            </h5>
                        </div>
                        <div class="card-body">
                            @if (!string.IsNullOrEmpty(job.Parameters))
                            {
                                <div class="mb-3">
                                    <label class="form-label fw-bold">Parameters</label>
                                    <div class="bg-light p-3 rounded border">
                                        <code class="text-dark">@job.Parameters</code>
                                        <button class="btn btn-sm btn-outline-secondary ms-2" @onclick="() => CopyToClipboard(job.Parameters)">
                                            <i class="fas fa-copy"></i>
                                        </button>
                                    </div>
                                </div>
                            }

                            @if (job.JobType == JobType.ApiEndpoint && !string.IsNullOrEmpty(job.ApiEndpointAddress))
                            {
                                <div class="mb-3">
                                    <label class="form-label fw-bold">
                                        <i class="fas fa-link me-2 text-primary"></i>API Endpoint
                                    </label>
                                    <div class="input-group">
                                        <input type="text" class="form-control" value="@job.ApiEndpointAddress" readonly />
                                        <button class="btn btn-outline-secondary" @onclick="() => CopyToClipboard(job.ApiEndpointAddress)">
                                            <i class="fas fa-copy"></i>
                                        </button>
                                        <a href="@job.ApiEndpointAddress" target="_blank" class="btn btn-outline-primary">
                                            <i class="fas fa-external-link-alt"></i> Test
                                        </a>
                                    </div>
                                </div>
                            }

                            @if (job.JobType == JobType.ExeFile && !string.IsNullOrEmpty(job.ExeFilePath))
                            {
                                <div class="mb-3">
                                    <label class="form-label fw-bold">
                                        <i class="fas fa-terminal me-2 text-success"></i>Executable Path
                                    </label>
                                    <div class="input-group">
                                        <input type="text" class="form-control" value="@job.ExeFilePath" readonly />
                                        <button class="btn btn-outline-secondary" @onclick="() => CopyToClipboard(job.ExeFilePath)">
                                            <i class="fas fa-copy"></i>
                                        </button>
                                    </div>
                                </div>
                            }

                            @if (job.JobType == JobType.DynamicCode && !string.IsNullOrEmpty(job.DynamicCode))
                            {
                                <div class="mb-3">
                                    <label class="form-label fw-bold">
                                        <i class="fas fa-code me-2 text-warning"></i>Dynamic Code
                                    </label>
                                    <div class="position-relative">
                                        <pre class="bg-dark text-light p-3 rounded border" style="max-height: 300px; overflow-y: auto;"><code>@job.DynamicCode</code></pre>
                                        <button class="btn btn-sm btn-outline-light position-absolute top-0 end-0 m-2" @onclick="() => CopyToClipboard(job.DynamicCode)">
                                            <i class="fas fa-copy"></i> Copy
                                        </button>
                                    </div>
                                </div>
                            }
                        </div>
                    </div>
                </div>
            </div>
        }

        <!-- Execution History Section -->
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header bg-secondary text-white d-flex justify-content-between align-items-center">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-history me-2"></i>Execution History
                        </h5>
                        <div class="d-flex gap-2">
                            <button class="btn btn-sm btn-outline-light" @onclick="RefreshHistory" disabled="@isLoadingHistory">
                                @if (isLoadingHistory)
                                {
                                    <span class="spinner-border spinner-border-sm me-1"></span>
                                }
                                else
                                {
                                    <i class="fas fa-sync-alt me-1"></i>
                                }
                                Refresh
                            </button>
                        </div>
                    </div>
                    <div class="card-body">
                        @if (isLoadingHistory)
                        {
                            <div class="text-center py-4">
                                <div class="spinner-border text-secondary" role="status">
                                    <span class="visually-hidden">Loading history...</span>
                                </div>
                                <p class="mt-2 text-muted">Loading execution history...</p>
                            </div>
                        }
                        else if (executionHistory == null || !executionHistory.Any())
                        {
                            <div class="text-center py-5">
                                <i class="fas fa-clock fa-3x text-muted mb-3"></i>
                                <h5 class="text-muted">No Execution History</h5>
                                <p class="text-muted">This job hasn't been executed yet.</p>
                                @if (job.Status == JobStatus.Published)
                                {
                                    <button class="btn btn-primary" @onclick="RunJob" disabled="@isRunning">
                                        <i class="fas fa-play me-2"></i>Run Job Now
                                    </button>
                                }
                            </div>
                        }
                        else
                        {
                            <!-- Execution Statistics -->
                            <div class="row mb-4">
                                <div class="col-md-3">
                                    <div class="text-center p-3 bg-light rounded">
                                        <h4 class="text-primary mb-1">@executionHistory.Count</h4>
                                        <small class="text-muted">Total Runs</small>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="text-center p-3 bg-light rounded">
                                        <h4 class="text-success mb-1">@executionHistory.Count(h => h.Success)</h4>
                                        <small class="text-muted">Successful</small>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="text-center p-3 bg-light rounded">
                                        <h4 class="text-danger mb-1">@executionHistory.Count(h => !h.Success)</h4>
                                        <small class="text-muted">Failed</small>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="text-center p-3 bg-light rounded">
                                        <h4 class="text-info mb-1">@(executionHistory.Any() ? Math.Round((double)executionHistory.Count(h => h.Success) / executionHistory.Count * 100, 1) : 0)%</h4>
                                        <small class="text-muted">Success Rate</small>
                                    </div>
                                </div>
                            </div>

                            <!-- Execution History Table -->
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead class="table-dark">
                                        <tr>
                                            <th>Execution Time</th>
                                            <th>Status</th>
                                            <th>Duration</th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @foreach (var history in executionHistory.Take(displayedHistoryCount))
                                        {
                                            <tr class="@(history.Success ? "" : "table-danger")">
                                                <td>
                                                    <div class="d-flex flex-column">
                                                        <span>@history.ExecutionTime.ToString("MMM dd, yyyy")</span>
                                                        <small class="text-muted">@history.ExecutionTime.ToString("HH:mm:ss")</small>
                                                    </div>
                                                </td>
                                                <td>
                                                    @if (history.Success)
                                                    {
                                                        <span class="badge bg-success">
                                                            <i class="fas fa-check-circle me-1"></i>Success
                                                        </span>
                                                    }
                                                    else
                                                    {
                                                        <span class="badge bg-danger">
                                                            <i class="fas fa-times-circle me-1"></i>Failed
                                                        </span>
                                                    }
                                                </td>
                                                <td>
                                                    <span class="text-muted">@GetRandomDuration() sec</span>
                                                </td>
                                                <td>
                                                    <button class="btn btn-sm btn-outline-info" @onclick="() => ShowExecutionLog(history)">
                                                        <i class="fas fa-file-alt me-1"></i>View Log
                                                    </button>
                                                </td>
                                            </tr>
                                        }
                                    </tbody>
                                </table>
                            </div>

                            @if (executionHistory.Count > displayedHistoryCount)
                            {
                                <div class="text-center mt-3">
                                    <button class="btn btn-outline-secondary" @onclick="LoadMoreHistory">
                                        <i class="fas fa-chevron-down me-2"></i>
                                        Show More (@(executionHistory.Count - displayedHistoryCount) remaining)
                                    </button>
                                </div>
                            }
                        }
                    </div>
                </div>
            </div>
        </div>
    }
</div>

<!-- Execution Log Modal -->
<div class="modal fade" id="logModal" tabindex="-1" aria-labelledby="logModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="logModalLabel">
                    <i class="fas fa-file-alt me-2"></i>Execution Log
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                @if (selectedHistory != null)
                {
                    <div class="row mb-3">
                        <div class="col-md-4">
                            <strong>Execution Time:</strong><br>
                            <span class="text-muted">@selectedHistory.ExecutionTime.ToString("MMM dd, yyyy HH:mm:ss")</span>
                        </div>
                        <div class="col-md-4">
                            <strong>Status:</strong><br>
                            @if (selectedHistory.Success)
                            {
                                <span class="badge bg-success">
                                    <i class="fas fa-check-circle me-1"></i>Success
                                </span>
                            }
                            else
                            {
                                <span class="badge bg-danger">
                                    <i class="fas fa-times-circle me-1"></i>Failed
                                </span>
                            }
                        </div>
                        <div class="col-md-4">
                            <strong>Duration:</strong><br>
                            <span class="text-muted">@GetRandomDuration() seconds</span>
                        </div>
                    </div>

                    <div class="mb-3">
                        <div class="d-flex justify-content-between align-items-center mb-2">
                            <strong>Log Output:</strong>
                            <button class="btn btn-sm btn-outline-secondary" @onclick="() => CopyToClipboard(selectedHistory.Log ?? string.Empty)">
                                <i class="fas fa-copy me-1"></i>Copy Log
                            </button>
                        </div>
                        <div class="bg-dark text-light p-3 rounded" style="max-height: 400px; overflow-y: auto; font-family: 'Courier New', monospace;">
                            @if (!string.IsNullOrEmpty(selectedHistory.Log))
                            {
                                @foreach (var line in selectedHistory.Log.Split('\n'))
                                {
                                    <div>@line</div>
                                }
                            }
                            else
                            {
                                <div class="text-muted fst-italic">No log output available</div>
                            }
                        </div>
                    </div>
                }
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                    <i class="fas fa-times me-1"></i>Close
                </button>
            </div>
        </div>
    </div>
</div>

@code {
    [Parameter] public int JobId { get; set; }

    private Job? job;
    private List<JobHistory> executionHistory = new();
    private JobHistory? selectedHistory;

    private string errorMessage = string.Empty;
    private string successMessage = string.Empty;
    private bool isLoading = true;
    private bool isLoadingHistory = true;
    private bool isRunning = false;
    private int displayedHistoryCount = 10;
    private Random random = new();

    protected override async Task OnInitializedAsync()
    {
        await LoadJobDetails();
        await LoadExecutionHistory();
    }

    private async Task LoadJobDetails()
    {
        isLoading = true;
        errorMessage = string.Empty;

        try
        {
            // TODO: Replace with actual API call
            job = GenerateSampleJob(JobId);

            if (job == null)
            {
                errorMessage = $"Job with ID {JobId} not found.";
            }
        }
        catch (Exception ex)
        {
            errorMessage = $"Error loading job details: {ex.Message}";
        }
        finally
        {
            isLoading = false;
        }
    }

    private async Task LoadExecutionHistory()
    {
        isLoadingHistory = true;

        try
        {
            // TODO: Replace with actual API call
            executionHistory = GenerateSampleExecutionHistory(JobId);
        }
        catch (Exception ex)
        {
            errorMessage = $"Error loading execution history: {ex.Message}";
        }
        finally
        {
            isLoadingHistory = false;
        }
    }

    private async Task RunJob()
    {
        if (job == null) return;

        isRunning = true;
        errorMessage = string.Empty;
        successMessage = string.Empty;

        try
        {
            // TODO: Replace with actual API call
            await Task.Delay(2000); // Simulate job execution

            successMessage = $"Job '{job.Name}' has been queued for execution.";

            // Refresh execution history after a delay
            await Task.Delay(1000);
            await LoadExecutionHistory();
        }
        catch (Exception ex)
        {
            errorMessage = $"Error running job: {ex.Message}";
        }
        finally
        {
            isRunning = false;
        }
    }

    private async Task RefreshHistory()
    {
        await LoadExecutionHistory();
    }

    private void LoadMoreHistory()
    {
        displayedHistoryCount = Math.Min(displayedHistoryCount + 10, executionHistory.Count);
    }

    private async Task ShowExecutionLog(JobHistory history)
    {
        selectedHistory = history;
        await JSRuntime.InvokeVoidAsync("eval", "new bootstrap.Modal(document.getElementById('logModal')).show()");
    }

    private async Task CopyToClipboard(string text)
    {
        try
        {
            await JSRuntime.InvokeVoidAsync("navigator.clipboard.writeText", text);
            successMessage = "Copied to clipboard!";

            // Clear success message after 3 seconds
            await Task.Delay(3000);
            successMessage = string.Empty;
            StateHasChanged();
        }
        catch
        {
            // Fallback for older browsers
            await JSRuntime.InvokeVoidAsync("eval", $"prompt('Copy this text:', '{text.Replace("'", "\\'")}')");
        }
    }

    // Helper Methods
    private bool HasJobTypeSpecificConfig()
    {
        if (job == null) return false;

        return job.JobType switch
        {
            JobType.ApiEndpoint => !string.IsNullOrEmpty(job.ApiEndpointAddress),
            JobType.ExeFile => !string.IsNullOrEmpty(job.ExeFilePath),
            JobType.DynamicCode => !string.IsNullOrEmpty(job.DynamicCode),
            _ => false
        };
    }

    private string GetJobTypeDisplayName(JobType jobType)
    {
        return jobType switch
        {
            JobType.ApiEndpoint => "API Endpoint",
            JobType.ExeFile => "Executable File",
            JobType.DynamicCode => "Dynamic Code",
            _ => jobType.ToString()
        };
    }

    private string GetJobTypeIcon(JobType jobType)
    {
        return jobType switch
        {
            JobType.ApiEndpoint => "fas fa-link",
            JobType.ExeFile => "fas fa-terminal",
            JobType.DynamicCode => "fas fa-code",
            _ => "fas fa-cog"
        };
    }

    private string GetStatusBadgeClass(JobStatus status)
    {
        return status switch
        {
            JobStatus.Draft => "bg-secondary",
            JobStatus.Pending => "bg-warning text-dark",
            JobStatus.Published => "bg-success",
            _ => "bg-secondary"
        };
    }

    private int GetRandomDuration()
    {
        return random.Next(1, 300); // Random duration between 1-300 seconds
    }

    // Sample Data Generation (TODO: Replace with actual API calls)
    private Job? GenerateSampleJob(int id)
    {
        var sampleJobs = new List<Job>
        {
            new Job
            {
                Id = 1,
                Name = "Daily Database Backup",
                Description = "Automated backup of the production database to cloud storage with compression and encryption.",
                JobType = JobType.ExeFile,
                JobFrequency = JobFrequency.Daily,
                Status = JobStatus.Published,
                Priority = 9,
                StartTime = DateTime.Now.AddDays(-30),
                EndTime = DateTime.Now.AddDays(365),
                RetryCount = 3,
                RetryDelay = 300,
                EmailNotification = true,
                ExeFilePath = "C:\\Scripts\\DatabaseBackup.exe",
                Parameters = "--database=ProductionDB --destination=s3://backup-bucket --compress=true --encrypt=true"
            },
            new Job
            {
                Id = 2,
                Name = "API Health Monitor",
                Description = "Continuous monitoring of critical API endpoints to ensure system availability and performance.",
                JobType = JobType.ApiEndpoint,
                JobFrequency = JobFrequency.Hourly,
                Status = JobStatus.Published,
                Priority = 10,
                StartTime = DateTime.Now.AddDays(-7),
                EndTime = DateTime.Now.AddDays(90),
                RetryCount = 2,
                RetryDelay = 60,
                EmailNotification = true,
                ApiEndpointAddress = "https://api.example.com/health/check"
            },
            new Job
            {
                Id = 3,
                Name = "Monthly Sales Report Generator",
                Description = "Generates comprehensive monthly sales reports with analytics and sends them to stakeholders.",
                JobType = JobType.DynamicCode,
                JobFrequency = JobFrequency.Monthly,
                Status = JobStatus.Draft,
                Priority = 6,
                StartTime = DateTime.Now,
                EndTime = DateTime.Now.AddDays(365),
                RetryCount = 1,
                RetryDelay = 120,
                EmailNotification = false,
                DynamicCode = @"using System;
using System.IO;
using System.Threading.Tasks;

Console.WriteLine(""Starting monthly sales report generation..."");

// Get current month data
var currentMonth = DateTime.Now.ToString(""yyyy-MM"");
Console.WriteLine($""Generating report for: {currentMonth}"");

// Simulate data processing
Console.WriteLine(""Fetching sales data..."");
await Task.Delay(2000);

Console.WriteLine(""Processing analytics..."");
await Task.Delay(1500);

Console.WriteLine(""Generating charts and graphs..."");
await Task.Delay(1000);

Console.WriteLine(""Compiling final report..."");
await Task.Delay(500);

Console.WriteLine(""Monthly sales report generated successfully!"");
Console.WriteLine($""Report saved to: /reports/sales-{currentMonth}.pdf"");"
            }
        };

        return sampleJobs.FirstOrDefault(j => j.Id == id);
    }

    private List<JobHistory> GenerateSampleExecutionHistory(int jobId)
    {
        var history = new List<JobHistory>();
        var random = new Random();

        // Generate 20 sample execution records
        for (int i = 0; i < 20; i++)
        {
            var executionTime = DateTime.Now.AddHours(-i * 2 - random.Next(0, 4));
            var success = random.Next(0, 10) > 1; // 90% success rate

            var log = success
                ? GenerateSuccessLog(executionTime, jobId)
                : GenerateFailureLog(executionTime, jobId);

            history.Add(new JobHistory
            {
                Id = i + 1,
                JobId = jobId,
                ExecutionTime = executionTime,
                Success = success,
                Log = log
            });
        }

        return history.OrderByDescending(h => h.ExecutionTime).ToList();
    }

    private string GenerateSuccessLog(DateTime executionTime, int jobId)
    {
        var logs = new[]
        {
            $@"[{executionTime:yyyy-MM-dd HH:mm:ss}] Job execution started
[{executionTime.AddSeconds(1):yyyy-MM-dd HH:mm:ss}] Initializing job environment
[{executionTime.AddSeconds(2):yyyy-MM-dd HH:mm:ss}] Loading configuration
[{executionTime.AddSeconds(5):yyyy-MM-dd HH:mm:ss}] Executing main process
[{executionTime.AddSeconds(45):yyyy-MM-dd HH:mm:ss}] Process completed successfully
[{executionTime.AddSeconds(46):yyyy-MM-dd HH:mm:ss}] Cleaning up resources
[{executionTime.AddSeconds(47):yyyy-MM-dd HH:mm:ss}] Job execution completed successfully

Exit Code: 0
Duration: 47 seconds
Memory Usage: 156 MB
CPU Usage: 12%",

            $@"[{executionTime:yyyy-MM-dd HH:mm:ss}] Starting job execution (ID: {jobId})
[{executionTime.AddSeconds(2):yyyy-MM-dd HH:mm:ss}] Validating input parameters
[{executionTime.AddSeconds(3):yyyy-MM-dd HH:mm:ss}] Establishing connections
[{executionTime.AddSeconds(8):yyyy-MM-dd HH:mm:ss}] Processing data batch 1/3
[{executionTime.AddSeconds(25):yyyy-MM-dd HH:mm:ss}] Processing data batch 2/3
[{executionTime.AddSeconds(42):yyyy-MM-dd HH:mm:ss}] Processing data batch 3/3
[{executionTime.AddSeconds(58):yyyy-MM-dd HH:mm:ss}] Finalizing results
[{executionTime.AddSeconds(60):yyyy-MM-dd HH:mm:ss}] All operations completed successfully

Total Records Processed: 15,847
Success Rate: 100%
Execution Time: 60 seconds"
        };

        return logs[random.Next(logs.Length)];
    }

    private string GenerateFailureLog(DateTime executionTime, int jobId)
    {
        var errors = new[]
        {
            "Connection timeout",
            "Access denied to resource",
            "Invalid configuration parameter",
            "Network unreachable",
            "Insufficient memory",
            "File not found",
            "Authentication failed",
            "Rate limit exceeded"
        };

        var selectedError = errors[random.Next(errors.Length)];

        return $@"[{executionTime:yyyy-MM-dd HH:mm:ss}] Job execution started
[{executionTime.AddSeconds(1):yyyy-MM-dd HH:mm:ss}] Initializing job environment
[{executionTime.AddSeconds(2):yyyy-MM-dd HH:mm:ss}] Loading configuration
[{executionTime.AddSeconds(5):yyyy-MM-dd HH:mm:ss}] Executing main process
[{executionTime.AddSeconds(15):yyyy-MM-dd HH:mm:ss}] ERROR: {selectedError}
[{executionTime.AddSeconds(16):yyyy-MM-dd HH:mm:ss}] Attempting retry (1/3)
[{executionTime.AddSeconds(25):yyyy-MM-dd HH:mm:ss}] ERROR: {selectedError}
[{executionTime.AddSeconds(26):yyyy-MM-dd HH:mm:ss}] Attempting retry (2/3)
[{executionTime.AddSeconds(35):yyyy-MM-dd HH:mm:ss}] ERROR: {selectedError}
[{executionTime.AddSeconds(36):yyyy-MM-dd HH:mm:ss}] Maximum retry attempts reached
[{executionTime.AddSeconds(37):yyyy-MM-dd HH:mm:ss}] Job execution failed

Exit Code: 1
Error: {selectedError}
Duration: 37 seconds
Stack Trace: at JobExecutor.Execute() line 42";
    }
}
