
using System;
using System.ComponentModel.DataAnnotations;

namespace ScheduleDot.Core
{
    public class JobHistory
    {
        public int Id { get; set; }

        [Required]
        public int JobId { get; set; }

        public DateTime ExecutionTime { get; set; }

        public bool Success { get; set; }

        public string? Log { get; set; }

        // Navigation property
        public Job? Job { get; set; }
    }
}
