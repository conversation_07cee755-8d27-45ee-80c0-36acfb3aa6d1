# ScheduleDot Product Requirements Document

## Elevator Pitch
ScheduleDot is a web application designed for busy programmers and it personel to effortlessly schedule new processes to run. It allows in runtime to define new jobs and schedule them. Users can run api endpoints, exe files, and dynamic code.

## Who is this App For
- **programmers and it personel:** Individuals who need a quick, streamlined way to run processes. 

## Functional Requirements
- **Job Definition View:**
  - A text input box for quickly naming a new schedule job.
  - A schedule job type dropdown.
  - A schedule job frequency dropdown.
  - A schedule job start time dropdown.
  - A schedule job end time dropdown.
  - A schedule job parameters input box.
  - A schedule job description input box.
  - A schedule job status dropdown.
  - A schedule job priority dropdown.
  - A schedule job retry count dropdown.
  - A schedule job retry delay dropdown.
  - A schedule job email notification dropdown.
  - A schedule job api endpoint address input box.(if type is api endpoint)
  - A schedule job exe file path input box.(if type is exe file)
  - A schedule job dynamic code input box.(if type is dynamic code)

- **Job History Views**
  - A list or grid view of past schedule jobs with visual indicators of status (draft, pending, published).
  - A detailed view of a selected schedule job with all the details.(past run times, run status, run output, run errors, etc. ) 

## User Stories
- **New Job Definition:**  
  _As an developer, I want to quickly define a new schedule job.
  
- **Job History diagnostics:**  
  _As an developer, I want to be able to view all the schedule jobs I've created and navigate through the pasts jobs and their logs, etc. 
  
## User Interface
- **Dashboard:**
  - A clean, intuitive central dashboard for accessing all features (job definition, history view).

  
- **Editing Interface:**
  - In-line editing tools that allow users to make quick adjustments to the generated content.
  

  
- **Responsive Design:**
  - Initially optimized for desktop use, with plans for future responsiveness to accommodate mobile and tablet views.

## Technical Stack
- **Frontend:** .NET Blazor.
- **Backend:** .Net Core, Ms Sql Database, If needed Redis as Cache, For Logs S3.
- **Authentication:** OAuth 