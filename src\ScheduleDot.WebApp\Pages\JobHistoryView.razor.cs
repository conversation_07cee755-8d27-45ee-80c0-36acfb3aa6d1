using Microsoft.AspNetCore.Components;
using Microsoft.JSInterop;
using ScheduleDot.Core;

namespace ScheduleDot.WebApp.Pages
{
    public partial class JobHistoryView
    {
        private List<Job> jobs = new();
        private List<Job> filteredJobs = new();
        private Dictionary<int, List<JobHistory>> jobHistories = new();

        private string errorMessage = string.Empty;
        private bool isLoading = true;

        // Filter and search
        private string searchTerm = string.Empty;
        private JobStatus? statusFilter = null;
        private JobType? typeFilter = null;

        // Pagination
        private int currentPage = 1;
        private int pageSize = 10;
        private int totalPages = 1;

        protected override async Task OnInitializedAsync()
        {
            await LoadJobs();
        }

        private async Task LoadJobs()
        {
            isLoading = true;
            errorMessage = string.Empty;

            try
            {
                // TODO: Load jobs from API
                // For now, we'll create some sample data
                jobs = GenerateSampleJobs();

                // TODO: Load job histories from API
                jobHistories = GenerateSampleJobHistories();

                ApplyFilters();
            }
            catch (Exception ex)
            {
                errorMessage = $"Error loading jobs: {ex.Message}";
            }
            finally
            {
                isLoading = false;
            }
        }

        private List<Job> GenerateSampleJobs()
        {
            return new List<Job>
        {
            new Job
            {
                Id = 1,
                Name = "Daily Data Backup",
                Description = "Backup database to cloud storage",
                JobType = JobType.ExeFile,
                JobFrequency = JobFrequency.Daily,
                Status = JobStatus.Published,
                Priority = 8,
                StartTime = DateTime.Now.AddDays(-30),
                EndTime = DateTime.Now.AddDays(365),
                ExeFilePath = "C:\\Scripts\\backup.exe"
            },
            new Job
            {
                Id = 2,
                Name = "API Health Check",
                Description = "Monitor API endpoint availability",
                JobType = JobType.ApiEndpoint,
                JobFrequency = JobFrequency.Hourly,
                Status = JobStatus.Published,
                Priority = 9,
                StartTime = DateTime.Now.AddDays(-7),
                EndTime = DateTime.Now.AddDays(90),
                ApiEndpointAddress = "https://api.example.com/health"
            },
            new Job
            {
                Id = 3,
                Name = "Generate Reports",
                Description = "Generate monthly sales reports",
                JobType = JobType.DynamicCode,
                JobFrequency = JobFrequency.Monthly,
                Status = JobStatus.Draft,
                Priority = 5,
                StartTime = DateTime.Now,
                EndTime = DateTime.Now.AddDays(365),
                DynamicCode = "Console.WriteLine(\"Generating reports...\");"
            }
        };
        }

        private Dictionary<int, List<JobHistory>> GenerateSampleJobHistories()
        {
            return new Dictionary<int, List<JobHistory>>
            {
                [1] = new List<JobHistory>
            {
                new JobHistory { Id = 1, JobId = 1, ExecutionTime = DateTime.Now.AddHours(-2), Success = true, Log = "Backup completed successfully" },
                new JobHistory { Id = 2, JobId = 1, ExecutionTime = DateTime.Now.AddDays(-1), Success = true, Log = "Backup completed successfully" }
            },
                [2] = new List<JobHistory>
            {
                new JobHistory { Id = 3, JobId = 2, ExecutionTime = DateTime.Now.AddMinutes(-30), Success = true, Log = "API is healthy" },
                new JobHistory { Id = 4, JobId = 2, ExecutionTime = DateTime.Now.AddHours(-1), Success = false, Log = "API timeout error" }
            }
            };
        }

        private void ApplyFilters()
        {
            var filtered = jobs.AsEnumerable();

            // Apply search filter
            if (!string.IsNullOrWhiteSpace(searchTerm))
            {
                filtered = filtered.Where(j =>
                    j.Name.Contains(searchTerm, StringComparison.OrdinalIgnoreCase) ||
                    (j.Description?.Contains(searchTerm, StringComparison.OrdinalIgnoreCase) ?? false));
            }

            // Apply status filter
            if (statusFilter.HasValue)
            {
                filtered = filtered.Where(j => j.Status == statusFilter.Value);
            }

            // Apply type filter
            if (typeFilter.HasValue)
            {
                filtered = filtered.Where(j => j.JobType == typeFilter.Value);
            }

            var allFiltered = filtered.ToList();
            totalPages = (int)Math.Ceiling((double)allFiltered.Count / pageSize);

            // Apply pagination
            filteredJobs = allFiltered
                .Skip((currentPage - 1) * pageSize)
                .Take(pageSize)
                .ToList();
        }

        private async Task OnSearchChanged(ChangeEventArgs e)
        {
            searchTerm = e.Value?.ToString() ?? string.Empty;
            currentPage = 1;
            ApplyFilters();
            await Task.CompletedTask;
        }

        private async Task OnFilterChanged()
        {
            currentPage = 1;
            ApplyFilters();
            await Task.CompletedTask;
        }

        private void ChangePage(int page)
        {
            if (page >= 1 && page <= totalPages)
            {
                currentPage = page;
                ApplyFilters();
            }
        }

        private async Task RefreshJobs()
        {
            await LoadJobs();
        }

        private async Task RunJob(int jobId)
        {
            try
            {
                // TODO: Call API to run job
                errorMessage = string.Empty;
                // For now, just show a message
                await JSRuntime.InvokeVoidAsync("alert", $"Job {jobId} execution started!");
            }
            catch (Exception ex)
            {
                errorMessage = $"Error running job: {ex.Message}";
            }
        }

        private async Task DeleteJob(int jobId)
        {
            try
            {
                var confirmed = await JSRuntime.InvokeAsync<bool>("confirm", "Are you sure you want to delete this job?");
                if (confirmed)
                {
                    // TODO: Call API to delete job
                    jobs.RemoveAll(j => j.Id == jobId);
                    ApplyFilters();
                    errorMessage = string.Empty;
                }
            }
            catch (Exception ex)
            {
                errorMessage = $"Error deleting job: {ex.Message}";
            }
        }

        private string GetJobTypeDisplayName(JobType jobType)
        {
            return jobType switch
            {
                JobType.ApiEndpoint => "API Endpoint",
                JobType.ExeFile => "Executable File",
                JobType.DynamicCode => "Dynamic Code",
                _ => jobType.ToString()
            };
        }

        private string GetStatusBadgeClass(JobStatus status)
        {
            return status switch
            {
                JobStatus.Draft => "bg-secondary",
                JobStatus.Pending => "bg-warning",
                JobStatus.Published => "bg-success",
                _ => "bg-secondary"
            };
        }
    }
}